﻿<UserControl x:Class="INC.View.MainPage.AppTitleView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.View.MainPage"
             xmlns:mainPage="clr-namespace:INC.ViewModel.MainPage;assembly=INC.ViewModel"
             xmlns:converter="clr-namespace:INC.ViewCore.Converter;assembly=INC.ViewCore"
             d:DataContext="{d:DesignInstance mainPage:AppTitleViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/ViewToggleButtonDictionary.xaml"></ResourceDictionary>


            </ResourceDictionary.MergedDictionaries>


            <converter:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converter:InversionBooleanToVisibilityConverter x:Key="InversionBooleanToVisibilityConverter"/>
        </ResourceDictionary>

    </UserControl.Resources>
    <Grid>
        <Grid  Visibility="{Binding IsShowTitle, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid.RowDefinitions>
                <RowDefinition Height="0"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="0"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="0"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="0"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="0"/>
            </Grid.ColumnDefinitions>


            <Image
                Grid.Row="1"
                Grid.Column="1"
                Width="20"
                Height="20"
                VerticalAlignment="Center"
                Source="../Resources/Images/logo.ico" />

            <TextBlock
                Grid.Row="1"
                Grid.Column="3"
                Text="云联智造"
                Margin="5,0"
                VerticalAlignment="Center"
                FontWeight="Bold"
                Foreground="{DynamicResource PrimaryBackground}" />

            <ToggleButton 
                Grid.Row="1"
                Grid.Column="5"
                Command="{Binding ToggleCollapseCommand}"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Background="Transparent"
                          BorderThickness="0"
                          DockPanel.Dock="Right"
                          Template="{StaticResource UserPanelButton}">
                <TextBlock 
                           Text="{Binding FontContent}"
                           FontFamily="../Resources/Fonts/#iconfont"
                           FontSize="22" />
            </ToggleButton>
        </Grid>

        <Grid  Visibility="{Binding IsShowTitle, Converter={StaticResource InversionBooleanToVisibilityConverter}}">
            <Grid.RowDefinitions>
                <RowDefinition Height="0"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="0"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="0"/>
            </Grid.ColumnDefinitions>

            <ToggleButton 
                Grid.Row="1"
                Grid.Column="1"
                Command="{Binding ToggleCollapseCommand}"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          Background="Transparent"
                          BorderThickness="0"
                          DockPanel.Dock="Right"
                          Template="{StaticResource UserPanelButton}">
                <TextBlock 
                           Text="{Binding FontContent}"
                           FontFamily="../Resources/Fonts/#iconfont"
                           FontSize="22" />
            </ToggleButton>
        </Grid>

    </Grid>
</UserControl>
