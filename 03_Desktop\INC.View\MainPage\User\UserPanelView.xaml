﻿<UserControl x:Class="INC.View.MainPage.User.UserPanelView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.View.MainPage.User" 
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:user="clr-namespace:INC.ViewModel.MainPage.User;assembly=INC.ViewModel"
             xmlns:converters="clr-namespace:INC.View.Converters"
             d:DataContext="{d:DesignInstance user:UserPanelViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/ViewBorderDictionary.xaml"/>
                <ResourceDictionary Source="../../Resources/ViewListViewDictionary.xaml"/>

            </ResourceDictionary.MergedDictionaries>

            <converters:MenuImageConverter x:Key="MenuConverter" />
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.Effect>
            <DropShadowEffect
               BlurRadius="20"
               Direction="180"
               Opacity="0.2"
               ShadowDepth="-10"
               Color="#1F000000" />
        </Grid.Effect>
        <Grid.InputBindings>
            <MouseBinding MouseAction="LeftClick" Command="{Binding MinCommand}"></MouseBinding>
        </Grid.InputBindings>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto" />
            <RowDefinition />
        </Grid.RowDefinitions>

       <Border Grid.Row="0"
           Grid.RowSpan="2"
           Opacity="1"
           Style="{DynamicResource HeaderBorderStyle}" />

        <StackPanel  Grid.Row="0" Margin="15" Orientation="Horizontal">
            <Image
               Width="40"
               Height="40"
               Source="../../Resources/Images/user.png" />
            <StackPanel Margin="10,0" VerticalAlignment="Center">
                <TextBlock Text="{Binding HeaderFunctionViewModel.UserName}" />
                <!--<TextBlock Margin="0,2" Text="{Binding EmailAddress}" />-->
            </StackPanel>
        </StackPanel>

       <ListBox
           x:Name="ListUserMenu"
           Grid.Row="1"
           Margin="15,0"
           ItemContainerStyle="{StaticResource ListMenuItemStyle}"
           ItemsSource="{Binding UserMenuItems}">
           <ListBox.ItemTemplate>
               <DataTemplate>
                   <StackPanel Margin="5" Orientation="Horizontal">
                       <b:Interaction.Triggers>
                           <b:EventTrigger EventName="MouseLeftButtonDown">
                               <b:InvokeCommandAction Command="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ListBox}, Path=DataContext.ExecuteUserActionCommand}" CommandParameter="{Binding ElementName=ListUserMenu, Path=SelectedItem.Key}" />
                           </b:EventTrigger>
                       </b:Interaction.Triggers>
                        <Image
                           Width="20"
                           Height="20"
                           Source="{Binding Icon, Converter={StaticResource MenuConverter}}" />
                       <TextBlock
                           Margin="10,0,0,0"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Style="{x:Null}"
                           Text="{Binding Key}" />
                   </StackPanel>
               </DataTemplate>
           </ListBox.ItemTemplate>
           <b:Interaction.Triggers>
               <b:EventTrigger EventName="SelectionChanged">
                   <b:InvokeCommandAction Command="{Binding ExecuteUserActionCommand}" CommandParameter="{Binding ElementName=ListUserMenu, Path=SelectedItem.Key}" />
               </b:EventTrigger>
           </b:Interaction.Triggers>
       </ListBox>
    </Grid>
</UserControl>
