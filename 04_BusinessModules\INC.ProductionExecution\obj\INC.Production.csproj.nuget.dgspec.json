{"format": 1, "restore": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj", "projectName": "INC.BusinessModuleCore", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\INC.FunctionModuleCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\INC.FunctionModuleCore.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\INC.ViewCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\INC.ViewCore.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj", "projectName": "INC.LoginBusinessModule", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj", "projectName": "INC.Production", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Syncfusion.Tools.WPF": {"target": "Package", "version": "[28.2.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\INC.FunctionModuleCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\INC.FunctionModuleCore.csproj", "projectName": "INC.FunctionModuleCore", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\INC.FunctionModuleCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Prism.Core": {"target": "Package", "version": "[9.0.537, )"}, "Serilog": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Syncfusion.XlsIO.Net.Core": {"target": "Package", "version": "[27.2.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj", "projectName": "INC.Common", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "Serilog": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\INC.ViewCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\INC.ViewCore.csproj", "projectName": "INC.ViewCore", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\INC.ViewCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.135, )"}, "Prism.Core": {"target": "Package", "version": "[9.0.537, )"}, "Prism.Unity": {"target": "Package", "version": "[9.0.537, )"}, "Syncfusion.Licensing": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.SfBusyIndicator.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.SfGrid.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.SfSkinManager.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.SfTreeView.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.Themes.MaterialDark.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.Themes.MaterialDarkBlue.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.Themes.MaterialLight.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.Themes.MaterialLightBlue.WPF": {"target": "Package", "version": "[27.2.4, )"}, "Syncfusion.Tools.WPF": {"target": "Package", "version": "[27.2.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj", "projectName": "INC.ViewModelCore", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.3.2, )"}, "Prism.Core": {"target": "Package", "version": "[9.0.537, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}