﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using INC.ViewModel.MainPage;
using INC.ViewModel.Menu;

namespace INC.View.MainPage
{
    /// <summary>
    /// MenuView.xaml 的交互逻辑
    /// </summary>
    public partial class MenuView : UserControl
    {
        private readonly AppTitleViewModel _appTitleViewModel;
        private readonly MenuViewModel _menuViewModel;

        public MenuView(
        AppTitleViewModel appTitleViewModel, MenuViewModel menuViewModel)
        {
            _appTitleViewModel = appTitleViewModel;
            InitializeComponent();

            _appTitleViewModel.OnMenuWidthChanged += _appTitleViewModel_OnMenuWidthChanged;

            treeViewItems.NodeExpanded += TreeViewItems_NodeExpanded;
            treeViewItems.SelectionChanged += TreeViewItems_SelectionChanged;
            _menuViewModel = menuViewModel;
        }

        private void TreeViewItems_NodeExpanded(object? sender, Syncfusion.UI.Xaml.TreeView.NodeExpandedCollapsedEventArgs e)
        {
            if (treeViewItems.ExpanderWidth == 0)
            {
                treeViewItems.ExpanderWidth = 15;

                _appTitleViewModel.Expand();
            }
        }

        private void TreeViewItems_SelectionChanged(object? sender, Syncfusion.UI.Xaml.TreeView.ItemSelectionChangedEventArgs e)
        {
            if (e != null && e.AddedItems != null)
            {
                if (e.AddedItems[0] is MenuItemViewModel item)
                {
                    if (this.DataContext is MenuViewModel viewModel)
                    {
                        _menuViewModel.ChangeIcon(e.AddedItems[0] as MenuItemViewModel);
                        viewModel.NavigationManager.Navigate(item.PageName);
                    }
                }
            }
        }

        private void _appTitleViewModel_OnMenuWidthChanged(object? sender, int e)
        {
            if (e == AppTitleViewModel.FoldedWidth)
            {
                treeViewItems.ExpanderWidth = 0;
                treeViewItems.CollapseAll();
            }
        }
    }
}
