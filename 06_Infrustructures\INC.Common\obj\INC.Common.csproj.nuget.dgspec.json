{"format": 1, "restore": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj", "projectName": "INC.Common", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "Serilog": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}