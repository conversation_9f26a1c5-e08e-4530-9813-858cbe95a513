{"version": 3, "targets": {"net8.0-windows7.0": {"CommunityToolkit.Mvvm/8.3.2": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "Dapper/2.1.35": {"type": "package", "compile": {"lib/net7.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Dapper.dll": {"related": ".xml"}}}, "Dapper.Contrib/2.0.78": {"type": "package", "dependencies": {"Dapper": "2.0.78"}, "compile": {"lib/net5.0/Dapper.Contrib.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Dapper.Contrib.dll": {"related": ".xml"}}}, "HslCommunication/12.1.3": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "System.IO.Ports": "6.0.0"}, "compile": {"lib/netstandard2.1/HslCommunication.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/HslCommunication.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "System.Text.Json": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"type": "package", "compile": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Prism.Container.Abstractions/9.0.106": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "compile": {"lib/net8.0/Prism.Container.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Prism.Container.Abstractions.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Prism.Container.Abstractions.targets": {}}}, "Prism.Container.Unity/9.0.106": {"type": "package", "dependencies": {"Prism.Container.Abstractions": "9.0.106", "Unity.Container": "5.11.11"}, "compile": {"lib/net8.0/Prism.Container.Unity.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Prism.Container.Unity.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Prism.Container.Unity.targets": {}}}, "Prism.Core/9.0.537": {"type": "package", "dependencies": {"Prism.Container.Abstractions": "9.0.106", "Prism.Events": "9.0.537"}, "compile": {"lib/net6.0/Prism.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Prism.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Prism.Core.targets": {}}}, "Prism.Events/9.0.537": {"type": "package", "compile": {"lib/net6.0/Prism.Events.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Prism.Events.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Prism.Events.targets": {}}}, "Prism.Unity/9.0.537": {"type": "package", "dependencies": {"Prism.Container.Unity": "9.0.106", "Prism.Wpf": "9.0.537"}, "compile": {"lib/net6.0-windows7.0/Prism.Unity.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Prism.Unity.Wpf.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "build": {"buildTransitive/Prism.Unity.targets": {}}}, "Prism.Wpf/9.0.537": {"type": "package", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.122", "Prism.Core": "9.0.537"}, "compile": {"lib/net6.0-windows7.0/Prism.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Prism.Wpf.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/9.0.0": {"type": "package", "dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "Serilog/4.1.0": {"type": "package", "compile": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "compile": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x86"}}}, "Syncfusion.Compression.Net.Core/27.2.4": {"type": "package", "compile": {"lib/net8.0/Syncfusion.Compression.Portable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Compression.Portable.dll": {"related": ".xml"}}}, "Syncfusion.Data.WPF/27.2.4": {"type": "package", "compile": {"lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Licensing/28.2.3": {"type": "package", "compile": {"lib/net8.0/Syncfusion.Licensing.dll": {}}, "runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {}}}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfGrid.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Data.WPF": "27.2.4", "Syncfusion.Licensing": "27.2.4", "Syncfusion.Shared.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfTreeView.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGridCommon.WPF": "27.2.4", "Syncfusion.Shared.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Shared.WPF/28.2.3": {"type": "package", "dependencies": {"Syncfusion.Licensing": "28.2.3"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Tools.WPF/28.2.3": {"type": "package", "dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.Shared.WPF": "28.2.3"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll": {"related": ".xml"}}}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Compression.Net.Core": "27.2.4", "Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0/Syncfusion.XlsIO.Portable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.XlsIO.Portable.dll": {"related": ".xml"}}}, "System.Data.SqlClient/4.9.0": {"type": "package", "dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "compile": {"lib/net8.0/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Data.SqlClient.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net8.0/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "[1.0.119]"}}, "System.IO.Pipelines/9.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.IO.Ports/9.0.0": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "9.0.0"}, "compile": {"lib/net8.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/9.0.0": {"type": "package", "dependencies": {"System.IO.Pipelines": "9.0.0", "System.Text.Encodings.Web": "9.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "Unity.Abstractions/5.11.7": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netcoreapp3.0/Unity.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.0/Unity.Abstractions.dll": {"related": ".pdb"}}}, "Unity.Container/5.11.11": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2", "Unity.Abstractions": "5.11.7"}, "compile": {"lib/netcoreapp3.0/Unity.Container.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.0/Unity.Container.dll": {"related": ".pdb"}}}, "INC.AutoUpdateFunctionModule/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.FunctionModuleCore": "1.0.0"}, "compile": {"bin/placeholder/INC.AutoUpdateFunctionModule.dll": {}}, "runtime": {"bin/placeholder/INC.AutoUpdateFunctionModule.dll": {}}}, "INC.BusinessModuleCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.FunctionModuleCore": "1.0.0", "INC.ViewCore": "1.0.0", "INC.ViewModelCore": "1.0.0"}, "compile": {"bin/placeholder/INC.BusinessModuleCore.dll": {}}, "runtime": {"bin/placeholder/INC.BusinessModuleCore.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Newtonsoft.Json": "13.0.1", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"bin/placeholder/INC.Common.dll": {}}, "runtime": {"bin/placeholder/INC.Common.dll": {}}}, "INC.CompactFunctionModule/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Dapper": "2.1.35", "Dapper.Contrib": "2.0.78", "INC.FunctionModuleCore": "1.0.0", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "System.Data.SQLite.Core": "1.0.119", "System.Data.SqlClient": "4.9.0"}, "compile": {"bin/placeholder/INC.CompactFunctionModule.dll": {}}, "runtime": {"bin/placeholder/INC.CompactFunctionModule.dll": {}}}, "INC.DeviceFunctionModule/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"HslCommunication": "12.1.3", "INC.FunctionModuleCore": "1.0.0", "System.IO.Ports": "9.0.0"}, "compile": {"bin/placeholder/INC.DeviceFunctionModule.dll": {}}, "runtime": {"bin/placeholder/INC.DeviceFunctionModule.dll": {}}}, "INC.FunctionModuleCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.Common": "1.0.0", "Prism.Core": "9.0.537", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "Syncfusion.XlsIO.Net.Core": "27.2.4"}, "compile": {"bin/placeholder/INC.FunctionModuleCore.dll": {}}, "runtime": {"bin/placeholder/INC.FunctionModuleCore.dll": {}}}, "INC.HalfScrap/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.LoginBusinessModule": "1.0.0"}, "compile": {"bin/placeholder/INC.HalfScrap.dll": {}}, "runtime": {"bin/placeholder/INC.HalfScrap.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.LoginBusinessModule/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.BusinessModuleCore": "1.0.0"}, "compile": {"bin/placeholder/INC.LoginBusinessModule.dll": {}}, "runtime": {"bin/placeholder/INC.LoginBusinessModule.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.Production/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.LoginBusinessModule": "1.0.0", "Syncfusion.Tools.WPF": "28.2.3"}, "compile": {"bin/placeholder/INC.Production.dll": {}}, "runtime": {"bin/placeholder/INC.Production.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.View/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.HalfScrap": "1.0.0", "INC.Production": "1.0.0", "INC.ViewModel": "1.0.0"}, "compile": {"bin/placeholder/INC.View.dll": {}}, "runtime": {"bin/placeholder/INC.View.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.ViewCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.ViewModelCore": "1.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.135", "Prism.Core": "9.0.537", "Prism.Unity": "9.0.537", "Syncfusion.Licensing": "27.2.4", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGrid.WPF": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4", "Syncfusion.SfTreeView.WPF": "27.2.4", "Syncfusion.Themes.MaterialDark.WPF": "27.2.4", "Syncfusion.Themes.MaterialDarkBlue.WPF": "27.2.4", "Syncfusion.Themes.MaterialLight.WPF": "27.2.4", "Syncfusion.Themes.MaterialLightBlue.WPF": "27.2.4", "Syncfusion.Tools.WPF": "27.2.4"}, "compile": {"bin/placeholder/INC.ViewCore.dll": {}}, "runtime": {"bin/placeholder/INC.ViewCore.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.ViewModel/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.HalfScrap": "1.0.0", "INC.LoginBusinessModule": "1.0.0", "INC.Production": "1.0.0"}, "compile": {"bin/placeholder/INC.ViewModel.dll": {}}, "runtime": {"bin/placeholder/INC.ViewModel.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.ViewModelCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"CommunityToolkit.Mvvm": "8.3.2", "INC.Common": "1.0.0", "Prism.Core": "9.0.537"}, "compile": {"bin/placeholder/INC.ViewModelCore.dll": {}}, "runtime": {"bin/placeholder/INC.ViewModelCore.dll": {}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.3.2": {"sha512": "m8EolE1A0Updj68WTsZSGI6VWb6mUqHPh7QFo0kt7+JPhYMNXRS1ch8TS/oITAdcxTLrwMOp3ku1KjeG1/Zdpg==", "type": "package", "path": "communitytoolkit.mvvm/8.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.3.2.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net6.0/CommunityToolkit.Mvvm.dll", "lib/net6.0/CommunityToolkit.Mvvm.pdb", "lib/net6.0/CommunityToolkit.Mvvm.xml", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "Dapper/2.1.35": {"sha512": "YKRwjVfrG7GYOovlGyQoMvr1/IJdn+7QzNXJxyMh0YfFF5yvDmTYaJOVYWsckreNjGsGSEtrMTpnzxTUq/tZQw==", "type": "package", "path": "dapper/2.1.35", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.1.35.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net5.0/Dapper.dll", "lib/net5.0/Dapper.xml", "lib/net7.0/Dapper.dll", "lib/net7.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml", "readme.md"]}, "Dapper.Contrib/2.0.78": {"sha512": "sUfDVIf8LlHNiz3MfUFodeyRiemfN1JFkPxYjCxFWlwNPg1iQ49mB+0E89TkywWs4X8fiRWOVDQgtH5FtzK5Kw==", "type": "package", "path": "dapper.contrib/2.0.78", "files": [".nupkg.metadata", ".signature.p7s", "dapper.contrib.2.0.78.nupkg.sha512", "dapper.contrib.nuspec", "lib/net461/Dapper.Contrib.dll", "lib/net461/Dapper.Contrib.xml", "lib/net5.0/Dapper.Contrib.dll", "lib/net5.0/Dapper.Contrib.xml", "lib/netstandard2.0/Dapper.Contrib.dll", "lib/netstandard2.0/Dapper.Contrib.xml"]}, "HslCommunication/12.1.3": {"sha512": "rLuKLW9EMtcA8ztc3sFioUCmz85xYnz6CxIH2+eZF4KNVSBFr+p7iViUjeNLY4W7TIX9qs3OQibBJhYNdnAtdA==", "type": "package", "path": "hslcommunication/12.1.3", "files": [".nupkg.metadata", ".signature.p7s", "hslcommunication.12.1.3.nupkg.sha512", "hslcommunication.nuspec", "lib/net20/HslCommunication.dll", "lib/net20/HslCommunication.xml", "lib/net35/HslCommunication.dll", "lib/net35/HslCommunication.xml", "lib/net451/HslCommunication.dll", "lib/net451/HslCommunication.xml", "lib/netstandard2.0/HslCommunication.dll", "lib/netstandard2.0/HslCommunication.xml", "lib/netstandard2.1/HslCommunication.dll", "lib/netstandard2.1/HslCommunication.xml"]}, "Microsoft.Extensions.Configuration/9.0.0": {"sha512": "YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "type": "package", "path": "microsoft.extensions.configuration/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"sha512": "lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"sha512": "RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"sha512": "4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"sha512": "WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"sha512": "fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"sha512": "uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"sha512": "3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"sha512": "jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.0": {"sha512": "N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "type": "package", "path": "microsoft.extensions.primitives/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"sha512": "r8qBEXmQfORso2+MVHnt8PSH4761zJ0SIxgQTSEDVLU97EN2FZdG6/ZCYUPhQy+OrPKgnpYBCAs3PS6Bs7wRsg==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.135", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net462/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net462/Microsoft.Xaml.Behaviors.dll", "lib/net462/Microsoft.Xaml.Behaviors.pdb", "lib/net462/Microsoft.Xaml.Behaviors.xml", "lib/net6.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Prism.Container.Abstractions/9.0.106": {"sha512": "QNOERNOqsxvAa8pbWjqFB872DkvYK/cVRrcFO5vJYgWTIKBd8xfaI/jaZ0qeXLYVDz0nrvgJTZVVnip6+68dCw==", "type": "package", "path": "prism.container.abstractions/9.0.106", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Container.Abstractions.targets", "lib/net462/Prism.Container.Abstractions.dll", "lib/net462/Prism.Container.Abstractions.pdb", "lib/net47/Prism.Container.Abstractions.dll", "lib/net47/Prism.Container.Abstractions.pdb", "lib/net6.0/Prism.Container.Abstractions.dll", "lib/net6.0/Prism.Container.Abstractions.pdb", "lib/net7.0/Prism.Container.Abstractions.dll", "lib/net7.0/Prism.Container.Abstractions.pdb", "lib/net8.0/Prism.Container.Abstractions.dll", "lib/net8.0/Prism.Container.Abstractions.pdb", "lib/netstandard2.0/Prism.Container.Abstractions.dll", "lib/netstandard2.0/Prism.Container.Abstractions.pdb", "lib/netstandard2.1/Prism.Container.Abstractions.dll", "lib/netstandard2.1/Prism.Container.Abstractions.pdb", "prism-logo.png", "prism.container.abstractions.9.0.106.nupkg.sha512", "prism.container.abstractions.nuspec"]}, "Prism.Container.Unity/9.0.106": {"sha512": "QRakEz+1HG7PGETsEWQnHED4tmp7Ir/lVIVo0TySER1ACqNGNQgAfSgza+B/WMl/SadHhrz+HlTVQw3+PrAFWQ==", "type": "package", "path": "prism.container.unity/9.0.106", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Container.Unity.targets", "lib/net462/Prism.Container.Unity.dll", "lib/net462/Prism.Container.Unity.pdb", "lib/net6.0/Prism.Container.Unity.dll", "lib/net6.0/Prism.Container.Unity.pdb", "lib/net7.0/Prism.Container.Unity.dll", "lib/net7.0/Prism.Container.Unity.pdb", "lib/net8.0/Prism.Container.Unity.dll", "lib/net8.0/Prism.Container.Unity.pdb", "lib/netstandard2.0/Prism.Container.Unity.dll", "lib/netstandard2.0/Prism.Container.Unity.pdb", "prism-logo.png", "prism.container.unity.9.0.106.nupkg.sha512", "prism.container.unity.nuspec"]}, "Prism.Core/9.0.537": {"sha512": "D7mEqPKLVNrD0g2WHCpC/MOKwn8h7X1liCWyjqjL7NCuxgwuhVLTG85E/ZPBkISrXdwvOQZ+bSY31bvP79FQlg==", "type": "package", "path": "prism.core/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Core.targets", "lib/net462/Prism.dll", "lib/net462/Prism.pdb", "lib/net462/Prism.xml", "lib/net47/Prism.dll", "lib/net47/Prism.pdb", "lib/net47/Prism.xml", "lib/net6.0/Prism.dll", "lib/net6.0/Prism.pdb", "lib/net6.0/Prism.xml", "lib/netstandard2.0/Prism.dll", "lib/netstandard2.0/Prism.pdb", "lib/netstandard2.0/Prism.xml", "prism-logo.png", "prism.core.9.0.537.nupkg.sha512", "prism.core.nuspec"]}, "Prism.Events/9.0.537": {"sha512": "Pzp5MGUuhAyKXZUbHVYNWLGF/eA3sScqDN6VrzbWlKj85R0IS0q+JXe99umynso2xhXAe+1jrQCCkgqmEFCBng==", "type": "package", "path": "prism.events/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Events.targets", "lib/net462/Prism.Events.dll", "lib/net462/Prism.Events.pdb", "lib/net462/Prism.Events.xml", "lib/net47/Prism.Events.dll", "lib/net47/Prism.Events.pdb", "lib/net47/Prism.Events.xml", "lib/net6.0/Prism.Events.dll", "lib/net6.0/Prism.Events.pdb", "lib/net6.0/Prism.Events.xml", "lib/netstandard2.0/Prism.Events.dll", "lib/netstandard2.0/Prism.Events.pdb", "lib/netstandard2.0/Prism.Events.xml", "prism-logo.png", "prism.events.9.0.537.nupkg.sha512", "prism.events.nuspec"]}, "Prism.Unity/9.0.537": {"sha512": "F2RjW2QZg2TsQxuYsRB0ldoacQw2xuZmaMM1LENfR+qbxPxBXC887yZ+PKeP9eWPP2sP3oVUqo09N8EWJLZXng==", "type": "package", "path": "prism.unity/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Unity.targets", "lib/net462/Prism.Unity.Wpf.dll", "lib/net462/Prism.Unity.Wpf.pdb", "lib/net462/Prism.Unity.Wpf.xml", "lib/net47/Prism.Unity.Wpf.dll", "lib/net47/Prism.Unity.Wpf.pdb", "lib/net47/Prism.Unity.Wpf.xml", "lib/net6.0-windows7.0/Prism.Unity.Wpf.dll", "lib/net6.0-windows7.0/Prism.Unity.Wpf.pdb", "lib/net6.0-windows7.0/Prism.Unity.Wpf.xml", "prism-logo.png", "prism.unity.9.0.537.nupkg.sha512", "prism.unity.nuspec"]}, "Prism.Wpf/9.0.537": {"sha512": "srsXhi7FRUFawsNoRkY67duMEGjZo3ff0FpqpkjeWkkAuLazlH1UmNVrvwnpaLQCBboexH/z6oGrLvpeocxgdw==", "type": "package", "path": "prism.wpf/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "lib/net462/Prism.Wpf.dll", "lib/net462/Prism.Wpf.pdb", "lib/net462/Prism.Wpf.xml", "lib/net47/Prism.Wpf.dll", "lib/net47/Prism.Wpf.pdb", "lib/net47/Prism.Wpf.xml", "lib/net6.0-windows7.0/Prism.Wpf.dll", "lib/net6.0-windows7.0/Prism.Wpf.pdb", "lib/net6.0-windows7.0/Prism.Wpf.xml", "prism-logo.png", "prism.wpf.9.0.537.nupkg.sha512", "prism.wpf.nuspec"]}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.0": {"sha512": "zF8HT4aoFZkWF4OxhFLxUNEfoIjyILg0aQhgIR3m+dbLE4yadMd7kdctMvPhYYaVpnilmBCIjiQsrxH4UC/JxQ==", "type": "package", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.android-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/libSystem.IO.Ports.Native.so", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "JVRoxUTXhyFfDak3GLbZh9oPjz+eVJwiZQWOU/TQ1Nj7us11GMc97IBsRzjGDtGJvFOWhGhEkka8SYmVcwpA2A==", "type": "package", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.android-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "QMWQv8nptbkzEDPUOmVwo3l/ve1pgApqv/eGY/eIJoNCGxUP6MYUu/GHdznRaBlSkuRyhFN8osVyqZMFKlBA7g==", "type": "package", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.android-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.0": {"sha512": "NRuTmUukSfpbv1wdJJXvWE/v1+aRHw5OxEODGeyKuFGy09uZIfFsdU1SPXB1cGPHsUaZRhZfOVel30zEgRQiUw==", "type": "package", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x86.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.android-x86.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.0": {"sha512": "l5/3/3LfkemzovK66DrxsbGXRXIgmHaqYaYdhFR09lawWbPHhq4HJ0u2FzO+/neidm8bJtJAV6+iixMDuYIBgg==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "q69FDpp5XSq3lJUMyMpUXBXTh6ekNM1NCnM5aYYiIx4AY1cH/rgLSwR4n2wQJqC6yuL0Z/epSf3KoYLYT8++Yg==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "kAOBq4UnR0B2UirRxLsPx4BIzt61Ydw40FFCe9NcFSncV6q+ikuhgN6eOrcaOcSu5QUiXacQRgFUX1Pux6ckYg==", "type": "package", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "yCpRhte4+7C6ULKGA4qLaXGjQJwoygqyzgUN9u2tkfyGkwBUS66SRr6nNx522+4ATI8ZFkgIIZIkTczY77rcZw==", "type": "package", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-bionic-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.0": {"sha512": "isaMOGqA4iIklwMt6wYTuPqj83D8DDUA2wainLqPjXaQ1Ri+5K8A+4J0BonjA/HMWtywBKnt2WGUXZ3DQN18ZA==", "type": "package", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-musl-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "yWsWQTf7r1aigde6EeoHzHhldoBw6fJ8AHR2ow4kobNuaS9Z/9rvLUFsGkAAY8GMUZadF5S1OGUsIzUd17RZBg==", "type": "package", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-musl-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "NxST2ZRBRGBjHWUnQwOYoyqFWHH4UcjAeTyjviSTOdwjSqq1JuGdp4sLzPzGDLiu4R7Per3QQ1GxYoLgAlIbOA==", "type": "package", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-musl-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "4bmb9oP1DIu2ArJ2MH2sNGbO5V3VrZ0+8lotr3cQ2G5hh66+0yHiYkwvlwP7gkSOsZPhANeX3cicqHYaDsroQA==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "k+VeOPbIx9A1/bmiw5pGBsuALGTA4UoC6SsGhcIMLyS6TMFgsjsOH1bAgim+/W1RdtR7dpPCWHNYhkrM8hXByA==", "type": "package", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "k1WC+Y7ht+7Omq5iW1v2Yz5CpaGGlLvlNsGS8cDAG0IN3sXUrPyUkC/40/zTL8g8/c3UFjrW0igXcwKNYa+ZuA==", "type": "package", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.maccatalyst-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.native.System.IO.Ports/9.0.0": {"sha512": "iWyR+xohLUht80x5MREqF7zYD0KqyVpoS9uTg9raG0ddx5pvJkCPC4eS2JdkRYY6AqPjfMiiOEZ02ZWHEBgOvg==", "type": "package", "path": "runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "ebr6uFzuICKkw9YePnCo7CdZFKYYhJZOJDJhACAKyzbT5WFvJWMyeACJIWS0uqndGMgWSc+D+UDdBu6CEpUOSg==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.0": {"sha512": "66DA4FKnfIdrkyd8Kqym06s+F/U5/7TZdkV1DllGivUNUGkC8TG5W/3D4rhLoGQRjg0uurkPWqrQXWfPEghRpQ==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Serilog/4.1.0": {"sha512": "u1aZI8HZ62LWlq5dZLFwm6jMax/sUwnWZSw5lkPsCt518cJBxFKoNmc7oSxe5aA5BgSkzy9rzwFGR/i/acnSPw==", "type": "package", "path": "serilog/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.1.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.Sinks.Console/6.0.0": {"sha512": "fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "type": "package", "path": "serilog.sinks.console/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net8.0/Serilog.Sinks.Console.dll", "lib/net8.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.6.0.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.File/6.0.0": {"sha512": "lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "type": "package", "path": "serilog.sinks.file/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.File.dll", "lib/net462/Serilog.Sinks.File.xml", "lib/net471/Serilog.Sinks.File.dll", "lib/net471/Serilog.Sinks.File.xml", "lib/net6.0/Serilog.Sinks.File.dll", "lib/net6.0/Serilog.Sinks.File.xml", "lib/net8.0/Serilog.Sinks.File.dll", "lib/net8.0/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.xml", "serilog-sink-nuget.png", "serilog.sinks.file.6.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"sha512": "dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "type": "package", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/System.Data.SQLite.dll", "lib/netstandard2.0/System.Data.SQLite.dll.altconfig", "lib/netstandard2.0/System.Data.SQLite.xml", "lib/netstandard2.1/System.Data.SQLite.dll", "lib/netstandard2.1/System.Data.SQLite.dll.altconfig", "lib/netstandard2.1/System.Data.SQLite.xml", "runtimes/linux-x64/native/SQLite.Interop.dll", "runtimes/osx-x64/native/SQLite.Interop.dll", "runtimes/win-x64/native/SQLite.Interop.dll", "runtimes/win-x86/native/SQLite.Interop.dll", "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512", "stub.system.data.sqlite.core.netstandard.nuspec"]}, "Syncfusion.Compression.Net.Core/27.2.4": {"sha512": "1+bVigMEnIxAbqVoIpIwOmX/LzJpHFCcSLJLsPo1x3Jk8khAXXPbZg/reH8mIPDEYVVNIifmx3RE6VJZz3/Uzw==", "type": "package", "path": "syncfusion.compression.net.core/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net6.0/Syncfusion.Compression.Portable.dll", "lib/net6.0/Syncfusion.Compression.Portable.xml", "lib/net7.0/Syncfusion.Compression.Portable.dll", "lib/net7.0/Syncfusion.Compression.Portable.xml", "lib/net8.0/Syncfusion.Compression.Portable.dll", "lib/net8.0/Syncfusion.Compression.Portable.xml", "lib/net9.0/Syncfusion.Compression.Portable.dll", "lib/net9.0/Syncfusion.Compression.Portable.xml", "lib/netstandard2.0/Syncfusion.Compression.Portable.dll", "lib/netstandard2.0/Syncfusion.Compression.Portable.xml", "syncfusion.compression.net.core.27.2.4.nupkg.sha512", "syncfusion.compression.net.core.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Data.WPF/27.2.4": {"sha512": "7KPOZZXA8aNGOX1V0VY6hV3X8xX2Fyu+Dcbe7VEIcZhSTS2E/CZ7PNNmczParX+VmDyz1tD7k8mFHEN5NPf4kg==", "type": "package", "path": "syncfusion.data.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net40/Syncfusion.Data.WPF.dll", "lib/net40/Syncfusion.Data.WPF.xml", "lib/net462/Syncfusion.Data.WPF.dll", "lib/net462/Syncfusion.Data.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Data.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Data.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Data.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Data.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Data.WPF.xml", "syncfusion.data.wpf.27.2.4.nupkg.sha512", "syncfusion.data.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Licensing/28.2.3": {"sha512": "EjsEHdkZwvViixabnrb2ZGmvg/a79Y01gLuOQzoMrRNUaOkQ/c71B+7eHoLUEJEt7H7OWNhlcKcbGtTmA75MBA==", "type": "package", "path": "syncfusion.licensing/28.2.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/MonoAndroid90/Syncfusion.Licensing.dll", "lib/Xamarin.Mac/Syncfusion.Licensing.dll", "lib/Xamarin.iOS10/Syncfusion.Licensing.dll", "lib/net462/Syncfusion.Licensing.dll", "lib/net6.0/Syncfusion.Licensing.dll", "lib/net7.0/Syncfusion.Licensing.dll", "lib/net8.0/Syncfusion.Licensing.dll", "lib/net9.0/Syncfusion.Licensing.dll", "lib/netstandard2.0/Syncfusion.Licensing.dll", "lib/uap10.0/Syncfusion.Licensing.dll", "syncfusion.licensing.28.2.3.nupkg.sha512", "syncfusion.licensing.nuspec", "syncfusion_logo.png"]}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"sha512": "z06nYGZkmeChFD1/ct8dD7N642LXUM26H2lJoRq/p5kJlkETi3ax1p0DzkdksXMNvSN/V1yr+Vf2NLPEGTX9IQ==", "type": "package", "path": "syncfusion.sfbusyindicator.wpf/27.2.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Design/Syncfusion.Design.Wpf.dll", "lib/net40/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net40/Design/Syncfusion.SfBusyIndicator.WPF.Expression.Design.dll", "lib/net40/Design/Syncfusion.SfBusyIndicator.Wpf.VisualStudio.Design.dll", "lib/net40/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net40/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net462/Design/Syncfusion.Design.Wpf.dll", "lib/net462/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.SfBusyIndicator.WPF.Expression.Design.dll", "lib/net462/Design/Syncfusion.SfBusyIndicator.Wpf.VisualStudio.Design.dll", "lib/net462/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net462/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.xml", "syncfusion.sfbusyindicator.wpf.27.2.4.nupkg.sha512", "syncfusion.sfbusyindicator.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.SfGrid.WPF/27.2.4": {"sha512": "852wa/nHP9/lCxg0p1Dyy+oqepcFwXNqpJzrBEyKTbKpHAN2ly8z2QjViNXfvOw7zjXh4OftGL2RUlWTrK6CSQ==", "type": "package", "path": "syncfusion.sfgrid.wpf/27.2.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net40/Design/Syncfusion.SfGrid.WPF.Expression.Design.dll", "lib/net40/Design/Syncfusion.SfGrid.WPF.VisualStudio.Design.dll", "lib/net40/Syncfusion.SfGrid.WPF.dll", "lib/net40/Syncfusion.SfGrid.WPF.xml", "lib/net462/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.SfGrid.WPF.Expression.Design.dll", "lib/net462/Design/Syncfusion.SfGrid.WPF.VisualStudio.Design.dll", "lib/net462/Syncfusion.SfGrid.WPF.dll", "lib/net462/Syncfusion.SfGrid.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.SfGrid.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfGrid.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.SfGrid.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfGrid.WPF.xml", "syncfusion.sfgrid.wpf.27.2.4.nupkg.sha512", "syncfusion.sfgrid.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"sha512": "I/IXKLY3rmzgbIsETpf7bz2HEVnEjDS7MUaQ9IgzCENzwrA8K42FMAFO3KZeWJmTGQZnR3YG+ukPfZsz18LGWA==", "type": "package", "path": "syncfusion.sfgridcommon.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net40/Syncfusion.SfGridCommon.WPF.dll", "lib/net40/Syncfusion.SfGridCommon.WPF.xml", "lib/net462/Syncfusion.SfGridCommon.WPF.dll", "lib/net462/Syncfusion.SfGridCommon.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfGridCommon.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfGridCommon.WPF.xml", "syncfusion.sfgridcommon.wpf.27.2.4.nupkg.sha512", "syncfusion.sfgridcommon.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"sha512": "omu/q1JaEydjM8F+WWIY7SGCa8/KMa87RfGaTIVMnWHru0b/Zcv6u/VsiSPuVTijMpp1shu+Kh5Phm0GnDRufA==", "type": "package", "path": "syncfusion.sfskinmanager.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.SfSkinManager.WPF.dll", "lib/net40/Syncfusion.SfSkinManager.WPF.xml", "lib/net462/Syncfusion.SfSkinManager.WPF.dll", "lib/net462/Syncfusion.SfSkinManager.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "syncfusion.sfskinmanager.wpf.27.2.4.nupkg.sha512", "syncfusion.sfskinmanager.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.SfTreeView.WPF/27.2.4": {"sha512": "LC/DZc7UG/5eBlL3HDjMGO5JzDRtjguH+yVttooY5odg6e9hP5X0znsSF83EcnOkwH7s0JR6OLnchBUQz9eu7w==", "type": "package", "path": "syncfusion.sftreeview.wpf/27.2.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net40/Design/Syncfusion.SfTreeView.WPF.Expression.Design.dll", "lib/net40/Design/Syncfusion.SfTreeView.WPF.VisualStudio.Design.dll", "lib/net40/Design/Syncfusion.SfTreeView.WPF.dll.Design.dll", "lib/net40/Syncfusion.SfTreeView.WPF.dll", "lib/net40/Syncfusion.SfTreeView.WPF.xml", "lib/net462/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.SfTreeView.WPF.Expression.Design.dll", "lib/net462/Design/Syncfusion.SfTreeView.WPF.VisualStudio.Design.dll", "lib/net462/Design/Syncfusion.SfTreeView.WPF.dll.Design.dll", "lib/net462/Syncfusion.SfTreeView.WPF.dll", "lib/net462/Syncfusion.SfTreeView.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.SfTreeView.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfTreeView.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.SfTreeView.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfTreeView.WPF.xml", "syncfusion.sftreeview.wpf.27.2.4.nupkg.sha512", "syncfusion.sftreeview.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.Shared.WPF/28.2.3": {"sha512": "qHQ2RH6d7vUf3NCAzHpOykzIYcZO6TfSA4DIrBis9TN3sl0g7u4WV3YMtt9szcRAGWBXqosCozwMbIbBh0IpgQ==", "type": "package", "path": "syncfusion.shared.wpf/28.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net462/Design/Syncfusion.Design.Wpf.dll", "lib/net462/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.Expression.Design.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.VisualStudio.Design.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.dll.Design.dll", "lib/net462/Syncfusion.Shared.WPF.dll", "lib/net462/Syncfusion.Shared.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Shared.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Shared.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Shared.WPF.xml", "syncfusion.shared.wpf.28.2.3.nupkg.sha512", "syncfusion.shared.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"sha512": "HFtbwZB+ZwzWP2K6UhJZDA4ORHHYs5evUwPwAC/UnL//05mWnYYKQY8+IXn2Hx4Wg1pIRUn/fc+sYvGvSS3AvQ==", "type": "package", "path": "syncfusion.themes.materialdark.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.xml", "syncfusion.themes.materialdark.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materialdark.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"sha512": "9ickWilfXrisH+bk1w4fZxbASCZQoANjbKulQ1KG8G1WdWj3M+wbrBIQ4dK577kbNpd9NtXJJK5NXuIJFWBkEQ==", "type": "package", "path": "syncfusion.themes.materialdarkblue.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "syncfusion.themes.materialdarkblue.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materialdarkblue.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"sha512": "OO6N/u+pzNVkY50uGSCz14ZSTA2PY38Qw4L9dehLm3pVOe1+4PBDQDxlaV2TWyfw+yaUF+sX2WTyxPfl23QGGw==", "type": "package", "path": "syncfusion.themes.materiallight.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.xml", "syncfusion.themes.materiallight.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materiallight.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"sha512": "wOdsaGE/gUtrJHiSJ4j0D2UkXAb0gvJQ0+91SOypmH5N8Z1ytIwW6XujZKBxAp95lm+PZV0sXLekmJ4nGaU6nQ==", "type": "package", "path": "syncfusion.themes.materiallightblue.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "syncfusion.themes.materiallightblue.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materiallightblue.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Tools.WPF/28.2.3": {"sha512": "vfAblki/RZQa8TBESUb0yF8iKwmURXJ4nl5rLRnp2W7FkHirLEitK6hTN3Jr7C9qjaDsECzFPK/3g4thHJhXiw==", "type": "package", "path": "syncfusion.tools.wpf/28.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net462/Design/Syncfusion.Design.Wpf.dll", "lib/net462/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.Tools.Wpf.Expression.Design.dll", "lib/net462/Design/Syncfusion.Tools.Wpf.VisualStudio.Design.dll", "lib/net462/Design/Syncfusion.Tools.Wpf.dll.Design.dll", "lib/net462/Syncfusion.Tools.WPF.dll", "lib/net462/Syncfusion.Tools.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.Tools.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Tools.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Tools.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.Tools.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Tools.WPF.xml", "syncfusion.tools.wpf.28.2.3.nupkg.sha512", "syncfusion.tools.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"sha512": "IlY9QaOZhjl+rJ0Q+I8PZZ7s5d26/Uw3hzQ9t08hEXfuq/kEkty3zbTmkYQZTOq6L4kaXjwApAGnQwxDCsPsow==", "type": "package", "path": "syncfusion.xlsio.net.core/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net6.0/Syncfusion.XlsIO.Portable.dll", "lib/net6.0/Syncfusion.XlsIO.Portable.xml", "lib/net7.0/Syncfusion.XlsIO.Portable.dll", "lib/net7.0/Syncfusion.XlsIO.Portable.xml", "lib/net8.0/Syncfusion.XlsIO.Portable.dll", "lib/net8.0/Syncfusion.XlsIO.Portable.xml", "lib/net9.0/Syncfusion.XlsIO.Portable.dll", "lib/net9.0/Syncfusion.XlsIO.Portable.xml", "lib/netstandard2.0/Syncfusion.XlsIO.Portable.dll", "lib/netstandard2.0/Syncfusion.XlsIO.Portable.xml", "syncfusion.xlsio.net.core.27.2.4.nupkg.sha512", "syncfusion.xlsio.net.core.nuspec", "syncfusion_logo.png"]}, "System.Data.SqlClient/4.9.0": {"sha512": "j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "type": "package", "path": "system.data.sqlclient/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Data.SqlClient.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.SqlClient.targets", "lib/net462/System.Data.SqlClient.dll", "lib/net462/System.Data.SqlClient.xml", "lib/net6.0/System.Data.SqlClient.dll", "lib/net6.0/System.Data.SqlClient.xml", "lib/net8.0/System.Data.SqlClient.dll", "lib/net8.0/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/System.Data.SqlClient.dll", "runtimes/unix/lib/net6.0/System.Data.SqlClient.xml", "runtimes/unix/lib/net8.0/System.Data.SqlClient.dll", "runtimes/unix/lib/net8.0/System.Data.SqlClient.xml", "runtimes/win/lib/net6.0/System.Data.SqlClient.dll", "runtimes/win/lib/net6.0/System.Data.SqlClient.xml", "runtimes/win/lib/net8.0/System.Data.SqlClient.dll", "runtimes/win/lib/net8.0/System.Data.SqlClient.xml", "system.data.sqlclient.4.9.0.nupkg.sha512", "system.data.sqlclient.nuspec"]}, "System.Data.SQLite.Core/1.0.119": {"sha512": "bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "type": "package", "path": "system.data.sqlite.core/1.0.119", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.core.1.0.119.nupkg.sha512", "system.data.sqlite.core.nuspec"]}, "System.IO.Pipelines/9.0.0": {"sha512": "eA3cinogwaNB4jdjQHOP3Z3EuyiDII7MT35jgtnsA4vkn0LUrrSHsU0nzHTzFzmaFYeKV7MYyMxOocFzsBHpTw==", "type": "package", "path": "system.io.pipelines/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Ports/9.0.0": {"sha512": "NfEWew48r4MxHUnOQL7nw/5JBsz9dli8TJYpXjsAQu8tHH0QCq2ly4QMCc8wS9EAi1jvaFgq7ELdfwxvrKWALQ==", "type": "package", "path": "system.io.ports/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net8.0/System.IO.Ports.dll", "lib/net8.0/System.IO.Ports.xml", "lib/net9.0/System.IO.Ports.dll", "lib/net9.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net8.0/System.IO.Ports.dll", "runtimes/unix/lib/net8.0/System.IO.Ports.xml", "runtimes/unix/lib/net9.0/System.IO.Ports.dll", "runtimes/unix/lib/net9.0/System.IO.Ports.xml", "runtimes/win/lib/net8.0/System.IO.Ports.dll", "runtimes/win/lib/net8.0/System.IO.Ports.xml", "runtimes/win/lib/net9.0/System.IO.Ports.dll", "runtimes/win/lib/net9.0/System.IO.Ports.xml", "system.io.ports.9.0.0.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"sha512": "wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/9.0.0": {"sha512": "e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "type": "package", "path": "system.text.encodings.web/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/net9.0/System.Text.Encodings.Web.dll", "lib/net9.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.9.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.0": {"sha512": "js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "type": "package", "path": "system.text.json/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.2": {"sha512": "BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "type": "package", "path": "system.threading.tasks.extensions/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.2.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Unity.Abstractions/5.11.7": {"sha512": "3ztwGEpe35UJlCUswXoi4uVDp8bJsgPsOmO71nZnNXh51II7t54AbezDbS6sR2z4QnMOpNGDaXbsEkyg6dIfOQ==", "type": "package", "path": "unity.abstractions/5.11.7", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Unity.Abstractions.dll", "lib/net40/Unity.Abstractions.pdb", "lib/net45/Unity.Abstractions.dll", "lib/net45/Unity.Abstractions.pdb", "lib/net46/Unity.Abstractions.dll", "lib/net46/Unity.Abstractions.pdb", "lib/net47/Unity.Abstractions.dll", "lib/net47/Unity.Abstractions.pdb", "lib/net48/Unity.Abstractions.dll", "lib/net48/Unity.Abstractions.pdb", "lib/netcoreapp1.0/Unity.Abstractions.dll", "lib/netcoreapp1.0/Unity.Abstractions.pdb", "lib/netcoreapp2.0/Unity.Abstractions.dll", "lib/netcoreapp2.0/Unity.Abstractions.pdb", "lib/netcoreapp3.0/Unity.Abstractions.dll", "lib/netcoreapp3.0/Unity.Abstractions.pdb", "lib/netstandard1.0/Unity.Abstractions.dll", "lib/netstandard1.0/Unity.Abstractions.pdb", "lib/netstandard2.0/Unity.Abstractions.dll", "lib/netstandard2.0/Unity.Abstractions.pdb", "unity.abstractions.5.11.7.nupkg.sha512", "unity.abstractions.nuspec"]}, "Unity.Container/5.11.11": {"sha512": "47u4MBG8hxV2ZBUK7LlXcZQW8yWSqUSCRG+2/TBA2CSkxkQlMfVUJ0RJODJsZgsiSgy4N0M8HIr7J88drYR/OQ==", "type": "package", "path": "unity.container/5.11.11", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Unity.Container.dll", "lib/net40/Unity.Container.pdb", "lib/net45/Unity.Container.dll", "lib/net45/Unity.Container.pdb", "lib/net46/Unity.Container.dll", "lib/net46/Unity.Container.pdb", "lib/net47/Unity.Container.dll", "lib/net47/Unity.Container.pdb", "lib/net48/Unity.Container.dll", "lib/net48/Unity.Container.pdb", "lib/netcoreapp1.0/Unity.Container.dll", "lib/netcoreapp1.0/Unity.Container.pdb", "lib/netcoreapp2.0/Unity.Container.dll", "lib/netcoreapp2.0/Unity.Container.pdb", "lib/netcoreapp3.0/Unity.Container.dll", "lib/netcoreapp3.0/Unity.Container.pdb", "lib/netstandard1.0/Unity.Container.dll", "lib/netstandard1.0/Unity.Container.pdb", "lib/netstandard2.0/Unity.Container.dll", "lib/netstandard2.0/Unity.Container.pdb", "unity.container.5.11.11.nupkg.sha512", "unity.container.nuspec"]}, "INC.AutoUpdateFunctionModule/1.0.0": {"type": "project", "path": "../../05_FunctionModules/INC.AutoUpdateFunctionModule/INC.AutoUpdateFunctionModule.csproj", "msbuildProject": "../../05_FunctionModules/INC.AutoUpdateFunctionModule/INC.AutoUpdateFunctionModule.csproj"}, "INC.BusinessModuleCore/1.0.0": {"type": "project", "path": "../../04_BusinessModules/INC.BusinessModuleCore/INC.BusinessModuleCore.csproj", "msbuildProject": "../../04_BusinessModules/INC.BusinessModuleCore/INC.BusinessModuleCore.csproj"}, "INC.Common/1.0.0": {"type": "project", "path": "../../06_Infrustructures/INC.Common/INC.Common.csproj", "msbuildProject": "../../06_Infrustructures/INC.Common/INC.Common.csproj"}, "INC.CompactFunctionModule/1.0.0": {"type": "project", "path": "../../05_FunctionModules/INC.CompactFunctionModule/INC.CompactFunctionModule.csproj", "msbuildProject": "../../05_FunctionModules/INC.CompactFunctionModule/INC.CompactFunctionModule.csproj"}, "INC.DeviceFunctionModule/1.0.0": {"type": "project", "path": "../../05_FunctionModules/INC.DeviceFunctionModule/INC.DeviceFunctionModule.csproj", "msbuildProject": "../../05_FunctionModules/INC.DeviceFunctionModule/INC.DeviceFunctionModule.csproj"}, "INC.FunctionModuleCore/1.0.0": {"type": "project", "path": "../../05_FunctionModules/INC.FunctionModuleCore/INC.FunctionModuleCore.csproj", "msbuildProject": "../../05_FunctionModules/INC.FunctionModuleCore/INC.FunctionModuleCore.csproj"}, "INC.HalfScrap/1.0.0": {"type": "project", "path": "../../04_BusinessModules/INC.ParticleMixing/INC.HalfScrap.csproj", "msbuildProject": "../../04_BusinessModules/INC.ParticleMixing/INC.HalfScrap.csproj"}, "INC.LoginBusinessModule/1.0.0": {"type": "project", "path": "../../04_BusinessModules/INC.LoginBusinessModule/INC.LoginBusinessModule.csproj", "msbuildProject": "../../04_BusinessModules/INC.LoginBusinessModule/INC.LoginBusinessModule.csproj"}, "INC.Production/1.0.0": {"type": "project", "path": "../../04_BusinessModules/INC.ProductionExecution/INC.Production.csproj", "msbuildProject": "../../04_BusinessModules/INC.ProductionExecution/INC.Production.csproj"}, "INC.View/1.0.0": {"type": "project", "path": "../../03_Desktop/INC.View/INC.View.csproj", "msbuildProject": "../../03_Desktop/INC.View/INC.View.csproj"}, "INC.ViewCore/1.0.0": {"type": "project", "path": "../../06_Infrustructures/INC.ViewCore/INC.ViewCore.csproj", "msbuildProject": "../../06_Infrustructures/INC.ViewCore/INC.ViewCore.csproj"}, "INC.ViewModel/1.0.0": {"type": "project", "path": "../../03_Desktop/INC.ViewModel/INC.ViewModel.csproj", "msbuildProject": "../../03_Desktop/INC.ViewModel/INC.ViewModel.csproj"}, "INC.ViewModelCore/1.0.0": {"type": "project", "path": "../../06_Infrustructures/INC.ViewModelCore/INC.ViewModelCore.csproj", "msbuildProject": "../../06_Infrustructures/INC.ViewModelCore/INC.ViewModelCore.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["INC.AutoUpdateFunctionModule >= 1.0.0", "INC.BusinessModuleCore >= 1.0.0", "INC.Common >= 1.0.0", "INC.CompactFunctionModule >= 1.0.0", "INC.DeviceFunctionModule >= 1.0.0", "INC.FunctionModuleCore >= 1.0.0", "INC.HalfScrap >= 1.0.0", "INC.LoginBusinessModule >= 1.0.0", "INC.Production >= 1.0.0", "INC.View >= 1.0.0", "INC.ViewCore >= 1.0.0", "INC.ViewModel >= 1.0.0", "INC.ViewModelCore >= 1.0.0", "Microsoft.Extensions.Configuration.Abstractions >= 9.0.0", "Microsoft.Extensions.Configuration.Binder >= 9.0.0", "Microsoft.Extensions.Configuration.FileExtensions >= 9.0.0", "Microsoft.Extensions.Configuration.Json >= 9.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\02_App\\INC.App\\INC.App.csproj", "projectName": "INC.App", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\02_App\\INC.App\\INC.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\02_App\\INC.App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\03_Desktop\\INC.ViewModel\\INC.ViewModel.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\03_Desktop\\INC.ViewModel\\INC.ViewModel.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\03_Desktop\\INC.View\\INC.View.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\03_Desktop\\INC.View\\INC.View.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ParticleMixing\\INC.HalfScrap.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ParticleMixing\\INC.HalfScrap.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.AutoUpdateFunctionModule\\INC.AutoUpdateFunctionModule.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.AutoUpdateFunctionModule\\INC.AutoUpdateFunctionModule.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.CompactFunctionModule\\INC.CompactFunctionModule.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.CompactFunctionModule\\INC.CompactFunctionModule.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.DeviceFunctionModule\\INC.DeviceFunctionModule.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.DeviceFunctionModule\\INC.DeviceFunctionModule.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\INC.FunctionModuleCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.FunctionModuleCore\\INC.FunctionModuleCore.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.Common\\INC.Common.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\INC.ViewCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewCore\\INC.ViewCore.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\06_Infrustructures\\INC.ViewModelCore\\INC.ViewModelCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}