﻿using System.Globalization;
using System.Windows.Data;

namespace INC.View.Converters;

public class MenuImageConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value != null)
        {
            return $"/Resources/Images/menus/{value.ToString()}.png";
        }
        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}