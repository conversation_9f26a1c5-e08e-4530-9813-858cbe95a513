﻿using INC.ViewModel.Model;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace INC.View.Converters;

public class DeviceStatusToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DeviceStatus status)
        {
            switch (status)
            {
                case DeviceStatus.Connected:
                    return new SolidColorBrush(Color.FromRgb(121, 189, 50));
                case DeviceStatus.DisConnected:
                    return new SolidColorBrush(Colors.Red);

            }
        }

        return new SolidColorBrush(Color.FromRgb(121, 189, 50));
    }


    public object ConvertBack(
        object value,
        Type targetType,
        object parameter,
        CultureInfo culture)
    {
        if (value is Brush brush)
        {
            var color = ((System.Windows.Media.SolidColorBrush)brush).Color;

            return Color.FromArgb(color.A, color.R, color.G, color.B);
        }

        return value;
    }
}