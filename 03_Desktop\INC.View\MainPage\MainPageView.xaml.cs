﻿using INC.ViewModel.MainPage;
using INC.ViewModel.Manager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace INC.View.MainPage
{
    /// <summary>
    /// MainPageView.xaml 的交互逻辑
    /// </summary>
    public partial class MainPageView : UserControl
    {
        public MainPageView()
        {
            InitializeComponent();
        }

        private void TabControlExt_OnCloseButtonClick(object sender, Syncfusion.Windows.Tools.Controls.CloseTabEventArgs e)
        {
            if (e.TargetTabItem != null)
            {
                if (this.DataContext is MainPageViewModel viewModel)
                    viewModel.NavigationManager.RemoveView(e.TargetTabItem.Content);
            }
        }

        private void TabControlExt_OnCloseAllTabs(object sender, Syncfusion.Windows.Tools.Controls.CloseTabEventArgs e)
        {
            if (e.ClosingTabItems != null)
            {
                if (this.DataContext is MainPageViewModel viewModel)
                {
                    foreach (var item in e.ClosingTabItems)
                        viewModel.NavigationManager.RemoveView(item);
                }
            }
        }

        private void TabControlExt_OnCloseOtherTabs(object sender, Syncfusion.Windows.Tools.Controls.CloseTabEventArgs e)
        {
            if (e.ClosingTabItems != null)
            {
                if (this.DataContext is MainPageViewModel viewModel)
                {
                    foreach (var item in e.ClosingTabItems)
                    {
                        if (item != e.TargetTabItem.Content)
                            viewModel.NavigationManager.RemoveView(item);
                    }
                }
            }
        }
    }
}
