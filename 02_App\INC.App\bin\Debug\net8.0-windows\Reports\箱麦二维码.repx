﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="EmployeeCard" DisplayName="EmployeeCard" SnapGridSize="25" ReportUnit="TenthsOfAMillimeter" Landscape="true" Margins="20, 20, 20, 21" PaperKind="Custom" PageWidth="900" PageHeight="700" Version="24.1" DataMember="Rep_Label_StockIn" DataSource="#Ref-0" Dpi="254" Font="微软雅黑, 9.75pt">
  <Parameters>
    <Item1 Ref="3" Description="Parameter" Name="Parameter" />
  </Parameters>
  <Bands>
    <Item1 Ref="4" ControlType="TopMarginBand" Name="TopMargin" HeightF="20" Dpi="254" />
    <Item2 Ref="5" ControlType="DetailBand" Name="Detail" SnapLinePadding="0,0,0,0,254" HeightF="348.79333" Dpi="254">
      <HierarchyPrintOptions Ref="6" Indent="50.8" />
      <Controls>
        <Item1 Ref="7" ControlType="XRBarCode" Name="barCode1" Module="5" AutoModule="true" Alignment="MiddleCenter" TextAlignment="MiddleCenter" ShowText="false" SizeF="156.74774,160" LocationFloat="702.0396,117.5949" Dpi="254" Padding="26,26,0,0,254">
          <Symbology Ref="8" Name="QRCode" CompactionMode="Byte" Version="Version1" />
          <ExpressionBindings>
            <Item1 Ref="9" EventName="BeforePrint" PropertyName="Text" Expression="[QRcode]" />
          </ExpressionBindings>
          <StylePriority Ref="10" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="11" ControlType="XRTable" Name="table2" TextAlignment="MiddleCenter" SizeF="860.00006,75" LocationFloat="0,273.79333" Dpi="254" Borders="All">
          <Rows>
            <Item1 Ref="12" ControlType="XRTableRow" Name="tableRow3" Weight="1" Dpi="254">
              <Cells>
                <Item1 Ref="13" ControlType="XRTableCell" Name="tableCell7" Weight="1" Text="tableCell7" Dpi="254" Padding="5,5,0,0,254">
                  <ExpressionBindings>
                    <Item1 Ref="14" EventName="BeforePrint" PropertyName="Text" Expression="[CustNo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="15" ControlType="XRTableCell" Name="tableCell10" Weight="1.2728766237074551" Text="tableCell10" Dpi="254">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="[ItemName]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="17" ControlType="XRTableCell" Name="tableCell15" Weight="1.160515661004384" Text="tableCell15" Dpi="254">
                  <ExpressionBindings>
                    <Item1 Ref="18" EventName="BeforePrint" PropertyName="Text" Expression="[ItemType]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="19" ControlType="XRTableCell" Name="tableCell8" Weight="1.160515661004384" Text="tableCell8" Dpi="254" Padding="5,5,0,0,254">
                  <ExpressionBindings>
                    <Item1 Ref="20" EventName="BeforePrint" PropertyName="Text" Expression="[Color]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="21" ControlType="XRTableCell" Name="tableCell9" Weight="0.5666077152881602" Text="tableCell9" Dpi="254" Padding="5,5,0,0,254">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[Quantity]" />
                  </ExpressionBindings>
                </Item5>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="23" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="24" ControlType="XRLabel" Name="label1" Multiline="true" Text="吴江聚辉纺织有限公司" TextAlignment="MiddleLeft" SizeF="699.5339,116.84" LocationFloat="127.3933,0" Dpi="254" Font="微软雅黑, 18pt" Padding="5,5,0,0,254">
          <StylePriority Ref="25" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="26" ControlType="XRPictureBox" Name="pictureBox1" ImageSource="img,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" Sizing="ZoomImage" ImageAlignment="TopLeft" SizeF="121.82067,116.84" LocationFloat="0,2.8199665E-14" Dpi="254" Borders="None" />
        <Item5 Ref="27" ControlType="XRTable" Name="table1" SizeF="860,160" LocationFloat="0,117.5949" Dpi="254">
          <Rows>
            <Item1 Ref="28" ControlType="XRTableRow" Name="tableRow1" Weight="1" Dpi="254">
              <Cells>
                <Item1 Ref="29" ControlType="XRTableCell" Name="tableCell5" Weight="0.6206936155796039" Text="tableCell5" TextAlignment="MiddleLeft" Dpi="254" Borders="Left, Top, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[PackageTitle]" />
                  </ExpressionBindings>
                  <StylePriority Ref="31" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="32" ControlType="XRTableCell" Name="tableCell1" Weight="2.1379191532611883" Text="tableCell1" TextAlignment="MiddleLeft" Dpi="254" Padding="5,5,0,0,254" Borders="Top, Right, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[PackageNo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="34" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="35" ControlType="XRTableCell" Name="tableCell2" Weight="0.6206936155796038" RowSpan="2" Dpi="254" Padding="5,5,0,0,254" Borders="All">
                  <StylePriority Ref="36" UseBorders="false" />
                </Item3>
              </Cells>
            </Item1>
            <Item2 Ref="37" ControlType="XRTableRow" Name="tableRow2" Weight="1" Dpi="254">
              <Cells>
                <Item1 Ref="38" ControlType="XRTableCell" Name="tableCell6" Weight="0.6206936155796038" Text="tableCell6" TextAlignment="MiddleLeft" Dpi="254" Borders="Left, Top, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[LocationTitle]" />
                  </ExpressionBindings>
                  <StylePriority Ref="40" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="41" ControlType="XRTableCell" Name="tableCell3" Weight="2.1379191532611888" Text="tableCell3" TextAlignment="MiddleLeft" Dpi="254" Padding="5,5,0,0,254" Borders="Top, Right, Bottom">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[LocationNo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="43" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="44" ControlType="XRTableCell" Name="tableCell4" Weight="0.6206936155796033" Text="tableCell4" Dpi="254" Padding="5,5,0,0,254">
                  <ExpressionBindings>
                    <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[QRcode]" />
                  </ExpressionBindings>
                </Item3>
              </Cells>
            </Item2>
          </Rows>
        </Item5>
      </Controls>
    </Item2>
    <Item3 Ref="46" ControlType="BottomMarginBand" Name="BottomMargin" SnapLinePadding="0,0,0,0,254" HeightF="21" Dpi="254" />
    <Item4 Ref="47" ControlType="DetailReportBand" Name="DetailReport1" Level="0" DataMember="Rep_Label_StockIn.Rep_Label_StockInStored Procedure" DataSource="#Ref-0" Dpi="254">
      <Bands>
        <Item1 Ref="48" ControlType="DetailBand" Name="Detail1" HeightF="75" Dpi="254">
          <HierarchyPrintOptions Ref="49" Indent="50.8" />
          <Controls>
            <Item1 Ref="50" ControlType="XRTable" Name="table3" TextAlignment="MiddleCenter" SizeF="860.00006,75" LocationFloat="0,1.4099832E-14" Dpi="254" Font="微软雅黑, 8pt" Borders="All">
              <Rows>
                <Item1 Ref="51" ControlType="XRTableRow" Name="tableRow4" Weight="1" Dpi="254">
                  <Cells>
                    <Item1 Ref="52" ControlType="XRTableCell" Name="tableCell11" Weight="1" Text="tableCell7" Dpi="254" Padding="5,5,0,0,254">
                      <ExpressionBindings>
                        <Item1 Ref="53" EventName="BeforePrint" PropertyName="Text" Expression="[CustNo]" />
                      </ExpressionBindings>
                    </Item1>
                    <Item2 Ref="54" ControlType="XRTableCell" Name="tableCell12" Weight="1.2728766237074551" Text="tableCell10" Dpi="254">
                      <ExpressionBindings>
                        <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[ItemName]" />
                      </ExpressionBindings>
                    </Item2>
                    <Item3 Ref="56" ControlType="XRTableCell" Name="tableCell16" Weight="1.160515661004384" Text="tableCell16" Dpi="254">
                      <ExpressionBindings>
                        <Item1 Ref="57" EventName="BeforePrint" PropertyName="Text" Expression="[ItemType]" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="58" ControlType="XRTableCell" Name="tableCell13" Weight="1.160515661004384" Text="tableCell8" Dpi="254" Padding="5,5,0,0,254">
                      <ExpressionBindings>
                        <Item1 Ref="59" EventName="BeforePrint" PropertyName="Text" Expression="[Color]" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="60" ControlType="XRTableCell" Name="tableCell14" Weight="0.5666077152881602" Text="tableCell9" Dpi="254" Padding="5,5,0,0,254">
                      <ExpressionBindings>
                        <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[Quantity]" />
                      </ExpressionBindings>
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="62" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </Bands>
    </Item4>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v24.1" Name="sqlDataSource1" Base64="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>