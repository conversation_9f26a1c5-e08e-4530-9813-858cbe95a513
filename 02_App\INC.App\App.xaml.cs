﻿using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.IO;
using System.Windows;
using System.Windows.Controls.Primitives;
using INC.CompactFunctionModule;
using INC.CompactFunctionModule.Database.Sqlite;
using INC.CompactFunctionModule.Database.SqlServer;
using INC.CompactFunctionModule.Log;
using INC.DeviceFunctionModule.Plc;
using INC.FunctionModuleCore;
using INC.FunctionModuleCore.Compact.Database.Sqlite;
using INC.FunctionModuleCore.Compact.Database.SqlServer;
using INC.FunctionModuleCore.Compact.Log;
using INC.FunctionModuleCore.Device.Plc;
using INC.FunctionModuleCore.Device.SerialPort;
using INC.ViewCore;
using INC.ViewCore.Theme;
using INC.ViewModelCore.Interface;
using Prism.Ioc;
using Prism.Navigation.Regions;
using Serilog;
using Syncfusion.SfSkinManager;
using Syncfusion.Themes.MaterialDarkBlue.WPF;
using Syncfusion.Themes.MaterialLight.WPF;
using Syncfusion.Windows.Shared;
using INC.ViewModelCore.Model.Language;
using HslCommunication.Language;
using INC.FunctionModuleCore.AutoUpdate;
using INC.View;
using INC.View.MainPage;
using INC.ViewModel;
using INC.ViewModelCore.Message;
using INC.ViewModelCore.Manager;
using INC.ViewModel.MainPage;
using INC.ViewModel.Interface;
using INC.ViewModel.Manager;
using INC.ViewModel.Menu;
using INC.LoginBusinessModule.Interface;
using INC.LoginBusinessModule.Manager;
using INC.View.Views;
using INC.ViewModel.ViewModels;
using INC.Common.Interface.Application;
using INC.View.MainPage.User;
using INC.ViewModel.MainPage.User;
using Microsoft.Extensions.Configuration;
using System.Windows.Threading;
using INC.ViewModelCore.Keys;
using System.Threading;
using INC.Common.Common;
using INC.ParticleMixing;
using INC.ProductionExecution;
using INC.ViewModel.Constants;
using INC.Common.Interface;
using INC.Common.Model.Settings;
using INC.Common.Utility;
using INC.DeviceFunctionModule.SerialPort;

namespace INC.TestApp
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : PrismApplication
    {
        private static Mutex _mutex = null;

        public App()
        {
            //默认配置.可以在模块中修改
            Log.Logger = new LoggerConfiguration()
                .Enrich.FromLogContext()
                .WriteTo.File("Logs\\log.txt",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30)
                .CreateLogger();

            
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Ngo9BigBOggjHTQxAR8/V1NMaF5cXmBCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWX1feHRXQ2JYWUB1X0c=");


            DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;
        }


        public IConfiguration Configuration { get; private set; }


        protected override void OnStartup(StartupEventArgs e)
        {
            //配置化
            var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            Configuration = builder.Build();

            AppSettingsManager.AppSettingModel = Configuration.Get<AppSettingModel>();

            //SfSkinManager.ApplyStylesOnApplication = true;

            CheckSingleton();

            base.OnStartup(e);
        }

        protected override void Initialize()
        {
            base.Initialize();
        }


        protected override void ConfigureViewModelLocator()
        {
            base.ConfigureViewModelLocator();

            ViewModelLocationProvider.Register<MainWindow, MainWindowViewModel>();
            ViewModelLocationProvider.Register<StatusBarView, StatusBarViewModel>();
            ViewModelLocationProvider.Register<AppTitleView, AppTitleViewModel>();
            ViewModelLocationProvider.Register<MenuView, MenuViewModel>();
            ViewModelLocationProvider.Register<HeaderFunctionView, HeaderFunctionViewModel>();
            ViewModelLocationProvider.Register<MainPageView, MainPageViewModel>();

            ViewModelLocationProvider.Register<UserPanelView, UserPanelViewModel>();


            ViewModelLocationProvider.Register<Production1View, Production1ViewModel>();
            ViewModelLocationProvider.Register<Production2View, Production2ViewModel>();
            ViewModelLocationProvider.Register<ParticleMixing1View, ParticleMixing1ViewModel>();
            ViewModelLocationProvider.Register<ParticleMixing2View, ParticleMixing2ViewModel>();

        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            containerRegistry.RegisterSingleton<ITimerProvider, INC.Common.Utility.Application.TimerProvider>();
            containerRegistry.RegisterSingleton<IUserSettingsManager, UserSettingsManager>();
            containerRegistry.RegisterSingleton<IUserManager, UserManager>();
            containerRegistry.RegisterSingleton<ISimpleAesHelper, SimpleAesHelper>();
            containerRegistry.RegisterSingleton<IServiceClient, ServiceClient>();
            containerRegistry.RegisterSingleton<IPrintManager, PrintManager>();

            containerRegistry.RegisterSingleton<IViewModelFactory, ViewModelFactory>();
            containerRegistry.RegisterSingleton<IThemeService, ThemeService>();
            containerRegistry.RegisterSingleton<IBusyIndicatorNotify, BusyIndicatorNotify>();
            containerRegistry.RegisterSingleton<IMenuManager, MenuManager>();
            containerRegistry.RegisterSingleton<AppTitleViewModel>();
            containerRegistry.RegisterSingleton<HeaderFunctionView>();
            containerRegistry.RegisterSingleton<HeaderFunctionViewModel>();
            containerRegistry.RegisterSingleton<MenuViewModel>();
            containerRegistry.RegisterSingleton<UserPanelView>();
            containerRegistry.RegisterSingleton<UserPanelViewModel>();

            ConfigureModule(containerRegistry);
        }

        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
        {
            moduleCatalog.AddModule<CompactLogFunctionModule>();
            moduleCatalog.AddModule<CompactSqlServerFunctionModule>();
            moduleCatalog.AddModule<CompactLocalCacheFunctionModule>();
            moduleCatalog.AddModule<DevicePlcFunctionModule>();
            moduleCatalog.AddModule<DeviceSerialPortFunctionModule>();
            moduleCatalog.AddModule<ViewCoreModule>();
            moduleCatalog.AddModule<INCViewModule>();
            //moduleCatalog.AddModule<AutoUpdateFunctionModule.AutoUpdateFunctionModule>();
            moduleCatalog.AddModule<LoginBusinessModule.LoginBusinessModule>();
            moduleCatalog.AddModule<ProductionExecutionBusinessModule>();
            moduleCatalog.AddModule<ParticleMixingBusinessModule>();
        }

        protected override void InitializeModules()
        {
            base.InitializeModules();
        }

        protected override void OnInitialized()
        {
            base.OnInitialized();

            var themeService = Container.Resolve<IThemeService>();
            themeService.SetTheme();
        }

        private void ConfigureModule(IContainerRegistry containerRegistry)
        {
            containerRegistry.Configure<CompactLogOptions>(options =>
            {
                options.LogFilePath = "C:\\Logs\\";
                options.RetainedFileCountLimit = 38;
                options.RollingInterval = RollingInterval.Day;
                options.AddCategories("Other", "Main", "Test");
            });


            containerRegistry.Configure<CompactSqlServerDbOptions>(options =>
            {
                options.AddOption("Db1", "Link1");
                options.AddOption("Db2", "Link2");
                options.AddOption("Db3", "Link3");
            });

            containerRegistry.Configure<CompactSqliteDbOptions>(options =>
            {
                options.DatabaseName = "LocalCache123.sqlite";
            });

            containerRegistry.Configure<DevicePlcOptions>(options =>
            {
                options.Category = PlcCategory.Siemens;
                options.IpAddress = "***********";
                options.Port = 502;
            } );

            containerRegistry.Configure<AutoUpdateOptions>(options =>
            {
                options.AppId = "YS";
                options.ServerBaseUrl = "https://*************:5550/api/Update/";
            });
        }


        #region Exception



        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            Log.Logger.Error(e.Exception.ToString());
            MessageBox.Show(e.Exception.ToString());
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Log.Logger.Error(e.ExceptionObject.ToString());
            MessageBox.Show(e.ExceptionObject.ToString());
        }

        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            Log.Logger.Error(e.Exception.ToString());

            MessageBox.Show(e.Exception.Message);
            Environment.Exit(-1);
        }

        #endregion


        /// <summary>
        /// 单例检查
        /// </summary>
        private void CheckSingleton()
        {
            const string appName = "INC.Desktop";

            _mutex = new Mutex(true, appName, out var createdNew);

            if (!createdNew)
            {
                //var localizer = Container.Resolve<ILocalizer>();

                //MessageBox.Show(localizer.GetText(ResourcesKeys.ApplicationAlreadyStarted));
                Application.Current.Shutdown();
            }
        }
    }

}
