{"version": 2, "dgSpecHash": "n4MP24jq95o=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ParticleMixing\\INC.HalfScrap.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.3.2\\communitytoolkit.mvvm.8.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.1\\microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.135\\microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.container.abstractions\\9.0.106\\prism.container.abstractions.9.0.106.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.container.unity\\9.0.106\\prism.container.unity.9.0.106.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.core\\9.0.537\\prism.core.9.0.537.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.events\\9.0.537\\prism.events.9.0.537.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.unity\\9.0.537\\prism.unity.9.0.537.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.wpf\\9.0.537\\prism.wpf.9.0.537.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.1.0\\serilog.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\6.0.0\\serilog.sinks.console.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\6.0.0\\serilog.sinks.file.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.compression.net.core\\27.2.4\\syncfusion.compression.net.core.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.data.wpf\\27.2.4\\syncfusion.data.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.licensing\\27.2.4\\syncfusion.licensing.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.sfbusyindicator.wpf\\27.2.4\\syncfusion.sfbusyindicator.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.sfgrid.wpf\\27.2.4\\syncfusion.sfgrid.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.sfgridcommon.wpf\\27.2.4\\syncfusion.sfgridcommon.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.sfskinmanager.wpf\\27.2.4\\syncfusion.sfskinmanager.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.sftreeview.wpf\\27.2.4\\syncfusion.sftreeview.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.shared.wpf\\27.2.4\\syncfusion.shared.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.themes.materialdark.wpf\\27.2.4\\syncfusion.themes.materialdark.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.themes.materialdarkblue.wpf\\27.2.4\\syncfusion.themes.materialdarkblue.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.themes.materiallight.wpf\\27.2.4\\syncfusion.themes.materiallight.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.themes.materiallightblue.wpf\\27.2.4\\syncfusion.themes.materiallightblue.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.tools.wpf\\27.2.4\\syncfusion.tools.wpf.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.xlsio.net.core\\27.2.4\\syncfusion.xlsio.net.core.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.2\\system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.2\\system.threading.tasks.extensions.4.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\unity.abstractions\\5.11.7\\unity.abstractions.5.11.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\unity.container\\5.11.11\\unity.container.5.11.11.nupkg.sha512"], "logs": []}