﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v21.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="英文入库单" DisplayName="英文入库单" SnapGridSize="31.75" ReportUnit="TenthsOfAMillimeter" Margins="20, 20, 20, 22" PaperKind="A4" PageWidth="2100" PageHeight="2970" Version="21.2" Dpi="254" Font="微软雅黑, 24pt">
  <Parameters>
    <Item1 Ref="2" Description="Parameter" AllowNull="true" Name="Parameter" />
  </Parameters>
  <Bands>
    <Item1 Ref="3" ControlType="DetailBand" Name="Detail" HeightF="0" Dpi="254">
      <HierarchyPrintOptions Ref="4" Indent="50.8" />
    </Item1>
    <Item2 Ref="5" ControlType="TopMarginBand" Name="TopMargin" HeightF="20" Dpi="254" />
    <Item3 Ref="6" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="22" Dpi="254" />
    <Item4 Ref="8" ControlType="DetailReportBand" Name="DetailReport" Level="0" DataMember="Rep_Label_EnglishStockIn" DataSource="#Ref-7" Dpi="254">
      <Bands>
        <Item1 Ref="9" ControlType="DetailBand" Name="Detail1" HeightF="3045.7495" TextAlignment="TopLeft" Dpi="254">
          <HierarchyPrintOptions Ref="10" Indent="50.8" />
          <Controls>
            <Item1 Ref="11" ControlType="XRLabel" Name="label11" Multiline="true" Text="M A D E I N C H I N A" TextAlignment="TopCenter" SizeF="1814.6951,217.17" LocationFloat="128.85669,2536.8862" Dpi="254" Font="Times New Roman, 40pt, style=Bold" Padding="5,5,0,0,254">
              <StylePriority Ref="12" UseFont="false" UseTextAlignment="false" />
            </Item1>
            <Item2 Ref="13" ControlType="XRLabel" Name="label10" Multiline="true" Text="L O G O" TextAlignment="MiddleRight" SizeF="571.5928,217.17" LocationFloat="1273.7424,2242.8337" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <StylePriority Ref="14" UseFont="false" UseTextAlignment="false" />
            </Item2>
            <Item3 Ref="15" ControlType="XRLabel" Name="label9" Multiline="true" Text="W I T H" TextAlignment="MiddleLeft" SizeF="571.5928,217.17" LocationFloat="234.69005,2242.8337" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <StylePriority Ref="16" UseFont="false" UseTextAlignment="false" />
            </Item3>
            <Item4 Ref="17" ControlType="XRLabel" Name="label8" Multiline="true" Text="label3" TextAlignment="MiddleLeft" SizeF="1708.8617,217.17" LocationFloat="234.69005,1919.8936" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="18" EventName="BeforePrint" PropertyName="Text" Expression="[Meas]" />
              </ExpressionBindings>
              <StylePriority Ref="19" UseFont="false" UseTextAlignment="false" />
            </Item4>
            <Item5 Ref="20" ControlType="XRLabel" Name="label7" Multiline="true" Text="label3" TextAlignment="MiddleLeft" SizeF="1708.8617,217.17" LocationFloat="234.69005,1630.8385" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[GWeight]" />
              </ExpressionBindings>
              <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
            </Item5>
            <Item6 Ref="23" ControlType="XRLabel" Name="label6" Multiline="true" Text="label3" TextAlignment="MiddleLeft" SizeF="1708.8617,217.17" LocationFloat="234.69005,1322.3818" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="24" EventName="BeforePrint" PropertyName="Text" Expression="[Quantity]" />
              </ExpressionBindings>
              <StylePriority Ref="25" UseFont="false" UseTextAlignment="false" />
            </Item6>
            <Item7 Ref="26" ControlType="XRLabel" Name="label5" Multiline="true" Text="label3" TextAlignment="MiddleLeft" SizeF="1708.8617,217.17" LocationFloat="234.69005,1018.11914" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[ItemType]" />
              </ExpressionBindings>
              <StylePriority Ref="28" UseFont="false" UseTextAlignment="false" />
            </Item7>
            <Item8 Ref="29" ControlType="XRLabel" Name="label4" Multiline="true" Text="label3" TextAlignment="MiddleLeft" SizeF="1708.8617,217.17" LocationFloat="234.69005,704.7968" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[Color]" />
              </ExpressionBindings>
              <StylePriority Ref="31" UseFont="false" UseTextAlignment="false" />
            </Item8>
            <Item9 Ref="32" ControlType="XRLabel" Name="label3" Multiline="true" Text="label3" TextAlignment="MiddleLeft" SizeF="1708.8617,217.17" LocationFloat="234.69005,388.35513" Dpi="254" Font="Times New Roman, 36pt" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[Item]" />
              </ExpressionBindings>
              <StylePriority Ref="34" UseFont="false" UseTextAlignment="false" />
            </Item9>
            <Item10 Ref="35" ControlType="XRLabel" Name="label2" Multiline="true" Text="label2" TextAlignment="BottomLeft" SizeF="1098.9508,300.72263" LocationFloat="844.601,47.79276" Dpi="254" Font="Times New Roman, 48pt, style=Bold" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[SalesOrderNo]" />
              </ExpressionBindings>
              <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
            </Item10>
            <Item11 Ref="38" ControlType="XRLabel" Name="label1" Multiline="true" Text="INVOICE" TextAlignment="BottomLeft" SizeF="571.5928,186.53403" LocationFloat="232.04422,161.98135" Dpi="254" Font="Times New Roman, 36pt, style=Bold" Padding="5,5,0,0,254">
              <ExpressionBindings>
                <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[INVOICE]" />
              </ExpressionBindings>
              <StylePriority Ref="40" UseFont="false" UseTextAlignment="false" />
            </Item11>
          </Controls>
          <StylePriority Ref="41" UseTextAlignment="false" />
        </Item1>
      </Bands>
    </Item4>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="7" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v21.2" Name="sqlDataSource1" Base64="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>