﻿<UserControl x:Class="INC.View.MainPage.MenuView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.View.MainPage"
             xmlns:mainPage="clr-namespace:INC.ViewModel.MainPage;assembly=INC.ViewModel"
             xmlns:converters="clr-namespace:INC.View.Converters"
             xmlns:Syncfusion="http://schemas.syncfusion.com/wpf"
             d:DataContext="{d:DesignInstance mainPage:MenuViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <converters:MenuImageConverter x:Key="MenuConverter" />
    </UserControl.Resources>
    <Grid Background="DarkGray">
        <Syncfusion:SfTreeView x:Name="treeViewItems"
                         HorizontalContentAlignment="Center"
                         AutoExpandMode="AllNodes"
                         BorderBrush="Transparent"
                         ChildPropertyName="Items"
                         ExpandActionTrigger="Node"
                         ExpanderPosition="End"
                         FocusVisualStyle="{x:Null}"
                         FullRowSelect="True"
                         IsAnimationEnabled="True"
                         ItemHeight="70"
                         ItemsSource="{Binding MenuItems}"
                         SelectionBackgroundColor="#0400D6"
                         SelectionForegroundColor="White"
                         Background="White">

            <Syncfusion:SfTreeView.ItemTemplate>
                <DataTemplate>
                    <StackPanel
                        Margin="0,5"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        Orientation="Horizontal">
                        <Image
                            Width="20"
                            Height="20"
                            Margin="10,0"
                            Source="{Binding Icon, Converter={StaticResource MenuConverter}}"
                            ToolTip="{Binding Title}" />
                        <TextBlock
                            Text="{Binding Title}"
                            VerticalAlignment="Center"
                            FontSize="20"
                            Style="{x:Null}" />
                    </StackPanel>
                </DataTemplate>
            </Syncfusion:SfTreeView.ItemTemplate>
        </Syncfusion:SfTreeView>
    </Grid>
</UserControl>
