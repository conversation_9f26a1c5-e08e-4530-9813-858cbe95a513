{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"INC.App/1.0.0": {"dependencies": {"INC.AutoUpdateFunctionModule": "1.0.0", "INC.BusinessModuleCore": "1.0.0", "INC.Common": "1.0.0", "INC.CompactFunctionModule": "1.0.0", "INC.DeviceFunctionModule": "1.0.0", "INC.FunctionModuleCore": "1.0.0", "INC.HalfScrap": "1.0.0", "INC.LoginBusinessModule": "1.0.0", "INC.Production": "1.0.0", "INC.View": "1.0.0", "INC.ViewCore": "1.0.0", "INC.ViewModel": "1.0.0", "INC.ViewModelCore": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0"}, "runtime": {"INC.App.dll": {}}}, "CommunityToolkit.Mvvm/8.3.2": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.3.0.0", "fileVersion": "8.3.2.1"}}}, "Dapper/2.1.35": {"runtime": {"lib/net7.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.35.13827"}}}, "Dapper.Contrib/2.0.78": {"dependencies": {"Dapper": "2.1.35"}, "runtime": {"lib/net5.0/Dapper.Contrib.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.78.45418"}}}, "HslCommunication/12.1.3": {"dependencies": {"Newtonsoft.Json": "13.0.1", "System.IO.Ports": "9.0.0"}, "runtime": {"lib/netstandard2.1/HslCommunication.dll": {"assemblyVersion": "12.1.3.0", "fileVersion": "12.1.3.0"}}}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.135.29210"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "Prism.Container.Abstractions/9.0.106": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Prism.Container.Abstractions.dll": {"assemblyVersion": "9.0.106.9543", "fileVersion": "9.0.106.9543"}}}, "Prism.Container.Unity/9.0.106": {"dependencies": {"Prism.Container.Abstractions": "9.0.106", "Unity.Container": "5.11.11"}, "runtime": {"lib/net8.0/Prism.Container.Unity.dll": {"assemblyVersion": "9.0.106.9543", "fileVersion": "9.0.106.9543"}}}, "Prism.Core/9.0.537": {"dependencies": {"Prism.Container.Abstractions": "9.0.106", "Prism.Events": "9.0.537"}, "runtime": {"lib/net6.0/Prism.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Events/9.0.537": {"runtime": {"lib/net6.0/Prism.Events.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Unity/9.0.537": {"dependencies": {"Prism.Container.Unity": "9.0.106", "Prism.Wpf": "9.0.537"}, "runtime": {"lib/net6.0-windows7.0/Prism.Unity.Wpf.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Wpf/9.0.537": {"dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.135", "Prism.Core": "9.0.537"}, "runtime": {"lib/net6.0-windows7.0/Prism.Wpf.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/9.0.0": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.0": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/4.1.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.1.0.0"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.1.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.1.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "1.0.119.0", "fileVersion": "1.0.119.0"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.119.0"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.119.0"}}}, "Syncfusion.Compression.Net.Core/27.2.4": {"runtime": {"lib/net8.0/Syncfusion.Compression.Portable.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Data.WPF/27.2.4": {"runtime": {"lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Licensing/28.2.3": {"runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {"assemblyVersion": "28.2.3.0", "fileVersion": "28.2.3.0"}}}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfGrid.WPF/27.2.4": {"dependencies": {"Syncfusion.Data.WPF": "27.2.4", "Syncfusion.Licensing": "28.2.3", "Syncfusion.Shared.WPF": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfTreeView.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGridCommon.WPF": "27.2.4", "Syncfusion.Shared.WPF": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Shared.WPF/28.2.3": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"assemblyVersion": "28.2.3.0", "fileVersion": "28.2.3.0"}}}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Tools.WPF/28.2.3": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.Shared.WPF": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll": {"assemblyVersion": "28.2.3.0", "fileVersion": "28.2.3.0"}}}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"dependencies": {"Syncfusion.Compression.Net.Core": "27.2.4", "Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0/Syncfusion.XlsIO.Portable.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "System.Data.SqlClient/4.9.0": {"dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "runtime": {"lib/net8.0/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.24.56208"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.900.24.56208"}, "runtimes/win/lib/net8.0/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.900.24.56208"}}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.IO.Pipelines/9.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.IO.Ports/9.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.0"}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {}, "System.Text.Encodings.Web/9.0.0": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Text.Json/9.0.0": {"dependencies": {"System.IO.Pipelines": "9.0.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Threading.Tasks.Extensions/4.5.2": {}, "Unity.Abstractions/5.11.7": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netcoreapp3.0/Unity.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Unity.Container/5.11.11": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2", "Unity.Abstractions": "5.11.7"}, "runtime": {"lib/netcoreapp3.0/Unity.Container.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "INC.AutoUpdateFunctionModule/1.0.0": {"dependencies": {"INC.FunctionModuleCore": "1.0.0"}, "runtime": {"INC.AutoUpdateFunctionModule.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.BusinessModuleCore/1.0.0": {"dependencies": {"INC.FunctionModuleCore": "1.0.0", "INC.ViewCore": "1.0.0", "INC.ViewModelCore": "1.0.0"}, "runtime": {"INC.BusinessModuleCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.Common/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"INC.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.CompactFunctionModule/1.0.0": {"dependencies": {"Dapper": "2.1.35", "Dapper.Contrib": "2.0.78", "INC.FunctionModuleCore": "1.0.0", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "System.Data.SQLite.Core": "1.0.119", "System.Data.SqlClient": "4.9.0"}, "runtime": {"INC.CompactFunctionModule.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.DeviceFunctionModule/1.0.0": {"dependencies": {"HslCommunication": "12.1.3", "INC.FunctionModuleCore": "1.0.0", "System.IO.Ports": "9.0.0"}, "runtime": {"INC.DeviceFunctionModule.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.FunctionModuleCore/1.0.0": {"dependencies": {"INC.Common": "1.0.0", "Prism.Core": "9.0.537", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "Syncfusion.XlsIO.Net.Core": "27.2.4"}, "runtime": {"INC.FunctionModuleCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.HalfScrap/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.LoginBusinessModule": "1.0.0"}, "runtime": {"INC.HalfScrap.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.LoginBusinessModule/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0"}, "runtime": {"INC.LoginBusinessModule.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.Production/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.LoginBusinessModule": "1.0.0", "Syncfusion.Tools.WPF": "28.2.3"}, "runtime": {"INC.Production.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.View/1.0.0": {"dependencies": {"INC.HalfScrap": "1.0.0", "INC.Production": "1.0.0", "INC.ViewModel": "1.0.0"}, "runtime": {"INC.View.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.ViewCore/1.0.0": {"dependencies": {"INC.ViewModelCore": "1.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.135", "Prism.Core": "9.0.537", "Prism.Unity": "9.0.537", "Syncfusion.Licensing": "28.2.3", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGrid.WPF": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4", "Syncfusion.SfTreeView.WPF": "27.2.4", "Syncfusion.Themes.MaterialDark.WPF": "27.2.4", "Syncfusion.Themes.MaterialDarkBlue.WPF": "27.2.4", "Syncfusion.Themes.MaterialLight.WPF": "27.2.4", "Syncfusion.Themes.MaterialLightBlue.WPF": "27.2.4", "Syncfusion.Tools.WPF": "28.2.3"}, "runtime": {"INC.ViewCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.ViewModel/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.HalfScrap": "1.0.0", "INC.LoginBusinessModule": "1.0.0", "INC.Production": "1.0.0"}, "runtime": {"INC.ViewModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "INC.ViewModelCore/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.3.2", "INC.Common": "1.0.0", "Prism.Core": "9.0.537"}, "runtime": {"INC.ViewModelCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DevExpress.XtraReports.v24.1/24.1.6.0": {"runtime": {"DevExpress.XtraReports.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.DataAccess.v24.1/24.1.6.0": {"runtime": {"DevExpress.DataAccess.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Printing.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Printing.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Data.v24.1/24.1.6.0": {"runtime": {"DevExpress.Data.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.XtraCharts.v24.1/24.1.6.0": {"runtime": {"DevExpress.XtraCharts.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Xpo.v24.1/24.1.6.0": {"runtime": {"DevExpress.Xpo.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Drawing.v24.1/24.1.6.0": {"runtime": {"DevExpress.Drawing.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Sparkline.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Sparkline.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.PivotGrid.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.PivotGrid.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Charts.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Charts.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.XtraGauges.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.XtraGauges.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Pdf.v24.1.Drawing/24.1.6.0": {"runtime": {"DevExpress.Pdf.v24.1.Drawing.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Pdf.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Pdf.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.RichEdit.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.RichEdit.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Office.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Office.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.CodeParser.v24.1/24.1.6.0": {"runtime": {"DevExpress.CodeParser.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.DataVisualization.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.DataVisualization.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}}}, "libraries": {"INC.App/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-m8EolE1A0Updj68WTsZSGI6VWb6mUqHPh7QFo0kt7+JPhYMNXRS1ch8TS/oITAdcxTLrwMOp3ku1KjeG1/Zdpg==", "path": "communitytoolkit.mvvm/8.3.2", "hashPath": "communitytoolkit.mvvm.8.3.2.nupkg.sha512"}, "Dapper/2.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-YKRwjVfrG7GYOovlGyQoMvr1/IJdn+7QzNXJxyMh0YfFF5yvDmTYaJOVYWsckreNjGsGSEtrMTpnzxTUq/tZQw==", "path": "dapper/2.1.35", "hashPath": "dapper.2.1.35.nupkg.sha512"}, "Dapper.Contrib/2.0.78": {"type": "package", "serviceable": true, "sha512": "sha512-sUfDVIf8LlHNiz3MfUFodeyRiemfN1JFkPxYjCxFWlwNPg1iQ49mB+0E89TkywWs4X8fiRWOVDQgtH5FtzK5Kw==", "path": "dapper.contrib/2.0.78", "hashPath": "dapper.contrib.2.0.78.nupkg.sha512"}, "HslCommunication/12.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-rLuKLW9EMtcA8ztc3sFioUCmz85xYnz6CxIH2+eZF4KNVSBFr+p7iViUjeNLY4W7TIX9qs3OQibBJhYNdnAtdA==", "path": "hslcommunication/12.1.3", "hashPath": "hslcommunication.12.1.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "path": "microsoft.extensions.configuration.json/9.0.0", "hashPath": "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"type": "package", "serviceable": true, "sha512": "sha512-r8qBEXmQfORso2+MVHnt8PSH4761zJ0SIxgQTSEDVLU97EN2FZdG6/ZCYUPhQy+OrPKgnpYBCAs3PS6Bs7wRsg==", "path": "microsoft.xaml.behaviors.wpf/1.1.135", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Prism.Container.Abstractions/9.0.106": {"type": "package", "serviceable": true, "sha512": "sha512-QNOERNOqsxvAa8pbWjqFB872DkvYK/cVRrcFO5vJYgWTIKBd8xfaI/jaZ0qeXLYVDz0nrvgJTZVVnip6+68dCw==", "path": "prism.container.abstractions/9.0.106", "hashPath": "prism.container.abstractions.9.0.106.nupkg.sha512"}, "Prism.Container.Unity/9.0.106": {"type": "package", "serviceable": true, "sha512": "sha512-QRakEz+1HG7PGETsEWQnHED4tmp7Ir/lVIVo0TySER1ACqNGNQgAfSgza+B/WMl/SadHhrz+HlTVQw3+PrAFWQ==", "path": "prism.container.unity/9.0.106", "hashPath": "prism.container.unity.9.0.106.nupkg.sha512"}, "Prism.Core/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-D7mEqPKLVNrD0g2WHCpC/MOKwn8h7X1liCWyjqjL7NCuxgwuhVLTG85E/ZPBkISrXdwvOQZ+bSY31bvP79FQlg==", "path": "prism.core/9.0.537", "hashPath": "prism.core.9.0.537.nupkg.sha512"}, "Prism.Events/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-Pzp5MGUuhAyKXZUbHVYNWLGF/eA3sScqDN6VrzbWlKj85R0IS0q+JXe99umynso2xhXAe+1jrQCCkgqmEFCBng==", "path": "prism.events/9.0.537", "hashPath": "prism.events.9.0.537.nupkg.sha512"}, "Prism.Unity/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-F2RjW2QZg2TsQxuYsRB0ldoacQw2xuZmaMM1LENfR+qbxPxBXC887yZ+PKeP9eWPP2sP3oVUqo09N8EWJLZXng==", "path": "prism.unity/9.0.537", "hashPath": "prism.unity.9.0.537.nupkg.sha512"}, "Prism.Wpf/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-srsXhi7FRUFawsNoRkY67duMEGjZo3ff0FpqpkjeWkkAuLazlH1UmNVrvwnpaLQCBboexH/z6oGrLvpeocxgdw==", "path": "prism.wpf/9.0.537", "hashPath": "prism.wpf.9.0.537.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zF8HT4aoFZkWF4OxhFLxUNEfoIjyILg0aQhgIR3m+dbLE4yadMd7kdctMvPhYYaVpnilmBCIjiQsrxH4UC/JxQ==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JVRoxUTXhyFfDak3GLbZh9oPjz+eVJwiZQWOU/TQ1Nj7us11GMc97IBsRzjGDtGJvFOWhGhEkka8SYmVcwpA2A==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QMWQv8nptbkzEDPUOmVwo3l/ve1pgApqv/eGY/eIJoNCGxUP6MYUu/GHdznRaBlSkuRyhFN8osVyqZMFKlBA7g==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NRuTmUukSfpbv1wdJJXvWE/v1+aRHw5OxEODGeyKuFGy09uZIfFsdU1SPXB1cGPHsUaZRhZfOVel30zEgRQiUw==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l5/3/3LfkemzovK66DrxsbGXRXIgmHaqYaYdhFR09lawWbPHhq4HJ0u2FzO+/neidm8bJtJAV6+iixMDuYIBgg==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-q69FDpp5XSq3lJUMyMpUXBXTh6ekNM1NCnM5aYYiIx4AY1cH/rgLSwR4n2wQJqC6yuL0Z/epSf3KoYLYT8++Yg==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kAOBq4UnR0B2UirRxLsPx4BIzt61Ydw40FFCe9NcFSncV6q+ikuhgN6eOrcaOcSu5QUiXacQRgFUX1Pux6ckYg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yCpRhte4+7C6ULKGA4qLaXGjQJwoygqyzgUN9u2tkfyGkwBUS66SRr6nNx522+4ATI8ZFkgIIZIkTczY77rcZw==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-isaMOGqA4iIklwMt6wYTuPqj83D8DDUA2wainLqPjXaQ1Ri+5K8A+4J0BonjA/HMWtywBKnt2WGUXZ3DQN18ZA==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yWsWQTf7r1aigde6EeoHzHhldoBw6fJ8AHR2ow4kobNuaS9Z/9rvLUFsGkAAY8GMUZadF5S1OGUsIzUd17RZBg==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxST2ZRBRGBjHWUnQwOYoyqFWHH4UcjAeTyjviSTOdwjSqq1JuGdp4sLzPzGDLiu4R7Per3QQ1GxYoLgAlIbOA==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4bmb9oP1DIu2ArJ2MH2sNGbO5V3VrZ0+8lotr3cQ2G5hh66+0yHiYkwvlwP7gkSOsZPhANeX3cicqHYaDsroQA==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k+VeOPbIx9A1/bmiw5pGBsuALGTA4UoC6SsGhcIMLyS6TMFgsjsOH1bAgim+/W1RdtR7dpPCWHNYhkrM8hXByA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k1WC+Y7ht+7Omq5iW1v2Yz5CpaGGlLvlNsGS8cDAG0IN3sXUrPyUkC/40/zTL8g8/c3UFjrW0igXcwKNYa+ZuA==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iWyR+xohLUht80x5MREqF7zYD0KqyVpoS9uTg9raG0ddx5pvJkCPC4eS2JdkRYY6AqPjfMiiOEZ02ZWHEBgOvg==", "path": "runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ebr6uFzuICKkw9YePnCo7CdZFKYYhJZOJDJhACAKyzbT5WFvJWMyeACJIWS0uqndGMgWSc+D+UDdBu6CEpUOSg==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-66DA4FKnfIdrkyd8Kqym06s+F/U5/7TZdkV1DllGivUNUGkC8TG5W/3D4rhLoGQRjg0uurkPWqrQXWfPEghRpQ==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-u1aZI8HZ62LWlq5dZLFwm6jMax/sUwnWZSw5lkPsCt518cJBxFKoNmc7oSxe5aA5BgSkzy9rzwFGR/i/acnSPw==", "path": "serilog/4.1.0", "hashPath": "serilog.4.1.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "Syncfusion.Compression.Net.Core/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-1+bVigMEnIxAbqVoIpIwOmX/LzJpHFCcSLJLsPo1x3Jk8khAXXPbZg/reH8mIPDEYVVNIifmx3RE6VJZz3/Uzw==", "path": "syncfusion.compression.net.core/27.2.4", "hashPath": "syncfusion.compression.net.core.27.2.4.nupkg.sha512"}, "Syncfusion.Data.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-7KPOZZXA8aNGOX1V0VY6hV3X8xX2Fyu+Dcbe7VEIcZhSTS2E/CZ7PNNmczParX+VmDyz1tD7k8mFHEN5NPf4kg==", "path": "syncfusion.data.wpf/27.2.4", "hashPath": "syncfusion.data.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Licensing/28.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-EjsEHdkZwvViixabnrb2ZGmvg/a79Y01gLuOQzoMrRNUaOkQ/c71B+7eHoLUEJEt7H7OWNhlcKcbGtTmA75MBA==", "path": "syncfusion.licensing/28.2.3", "hashPath": "syncfusion.licensing.28.2.3.nupkg.sha512"}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-z06nYGZkmeChFD1/ct8dD7N642LXUM26H2lJoRq/p5kJlkETi3ax1p0DzkdksXMNvSN/V1yr+Vf2NLPEGTX9IQ==", "path": "syncfusion.sfbusyindicator.wpf/27.2.4", "hashPath": "syncfusion.sfbusyindicator.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfGrid.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-852wa/nHP9/lCxg0p1Dyy+oqepcFwXNqpJzrBEyKTbKpHAN2ly8z2QjViNXfvOw7zjXh4OftGL2RUlWTrK6CSQ==", "path": "syncfusion.sfgrid.wpf/27.2.4", "hashPath": "syncfusion.sfgrid.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-I/IXKLY3rmzgbIsETpf7bz2HEVnEjDS7MUaQ9IgzCENzwrA8K42FMAFO3KZeWJmTGQZnR3YG+ukPfZsz18LGWA==", "path": "syncfusion.sfgridcommon.wpf/27.2.4", "hashPath": "syncfusion.sfgridcommon.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-omu/q1JaEydjM8F+WWIY7SGCa8/KMa87RfGaTIVMnWHru0b/Zcv6u/VsiSPuVTijMpp1shu+Kh5Phm0GnDRufA==", "path": "syncfusion.sfskinmanager.wpf/27.2.4", "hashPath": "syncfusion.sfskinmanager.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfTreeView.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-LC/DZc7UG/5eBlL3HDjMGO5JzDRtjguH+yVttooY5odg6e9hP5X0znsSF83EcnOkwH7s0JR6OLnchBUQz9eu7w==", "path": "syncfusion.sftreeview.wpf/27.2.4", "hashPath": "syncfusion.sftreeview.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Shared.WPF/28.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-qHQ2RH6d7vUf3NCAzHpOykzIYcZO6TfSA4DIrBis9TN3sl0g7u4WV3YMtt9szcRAGWBXqosCozwMbIbBh0IpgQ==", "path": "syncfusion.shared.wpf/28.2.3", "hashPath": "syncfusion.shared.wpf.28.2.3.nupkg.sha512"}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-HFtbwZB+ZwzWP2K6UhJZDA4ORHHYs5evUwPwAC/UnL//05mWnYYKQY8+IXn2Hx4Wg1pIRUn/fc+sYvGvSS3AvQ==", "path": "syncfusion.themes.materialdark.wpf/27.2.4", "hashPath": "syncfusion.themes.materialdark.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-9ickWilfXrisH+bk1w4fZxbASCZQoANjbKulQ1KG8G1WdWj3M+wbrBIQ4dK577kbNpd9NtXJJK5NXuIJFWBkEQ==", "path": "syncfusion.themes.materialdarkblue.wpf/27.2.4", "hashPath": "syncfusion.themes.materialdarkblue.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-OO6N/u+pzNVkY50uGSCz14ZSTA2PY38Qw4L9dehLm3pVOe1+4PBDQDxlaV2TWyfw+yaUF+sX2WTyxPfl23QGGw==", "path": "syncfusion.themes.materiallight.wpf/27.2.4", "hashPath": "syncfusion.themes.materiallight.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-wOdsaGE/gUtrJHiSJ4j0D2UkXAb0gvJQ0+91SOypmH5N8Z1ytIwW6XujZKBxAp95lm+PZV0sXLekmJ4nGaU6nQ==", "path": "syncfusion.themes.materiallightblue.wpf/27.2.4", "hashPath": "syncfusion.themes.materiallightblue.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Tools.WPF/28.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-vfAblki/RZQa8TBESUb0yF8iKwmURXJ4nl5rLRnp2W7FkHirLEitK6hTN3Jr7C9qjaDsECzFPK/3g4thHJhXiw==", "path": "syncfusion.tools.wpf/28.2.3", "hashPath": "syncfusion.tools.wpf.28.2.3.nupkg.sha512"}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-IlY9QaOZhjl+rJ0Q+I8PZZ7s5d26/Uw3hzQ9t08hEXfuq/kEkty3zbTmkYQZTOq6L4kaXjwApAGnQwxDCsPsow==", "path": "syncfusion.xlsio.net.core/27.2.4", "hashPath": "syncfusion.xlsio.net.core.27.2.4.nupkg.sha512"}, "System.Data.SqlClient/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "path": "system.data.sqlclient/4.9.0", "hashPath": "system.data.sqlclient.4.9.0.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.IO.Pipelines/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eA3cinogwaNB4jdjQHOP3Z3EuyiDII7MT35jgtnsA4vkn0LUrrSHsU0nzHTzFzmaFYeKV7MYyMxOocFzsBHpTw==", "path": "system.io.pipelines/9.0.0", "hashPath": "system.io.pipelines.9.0.0.nupkg.sha512"}, "System.IO.Ports/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfEWew48r4MxHUnOQL7nw/5JBsz9dli8TJYpXjsAQu8tHH0QCq2ly4QMCc8wS9EAi1jvaFgq7ELdfwxvrKWALQ==", "path": "system.io.ports/9.0.0", "hashPath": "system.io.ports.9.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "path": "system.threading.tasks.extensions/4.5.2", "hashPath": "system.threading.tasks.extensions.4.5.2.nupkg.sha512"}, "Unity.Abstractions/5.11.7": {"type": "package", "serviceable": true, "sha512": "sha512-3ztwGEpe35UJlCUswXoi4uVDp8bJsgPsOmO71nZnNXh51II7t54AbezDbS6sR2z4QnMOpNGDaXbsEkyg6dIfOQ==", "path": "unity.abstractions/5.11.7", "hashPath": "unity.abstractions.5.11.7.nupkg.sha512"}, "Unity.Container/5.11.11": {"type": "package", "serviceable": true, "sha512": "sha512-47u4MBG8hxV2ZBUK7LlXcZQW8yWSqUSCRG+2/TBA2CSkxkQlMfVUJ0RJODJsZgsiSgy4N0M8HIr7J88drYR/OQ==", "path": "unity.container/5.11.11", "hashPath": "unity.container.5.11.11.nupkg.sha512"}, "INC.AutoUpdateFunctionModule/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.BusinessModuleCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.CompactFunctionModule/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.DeviceFunctionModule/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.FunctionModuleCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.HalfScrap/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.LoginBusinessModule/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.Production/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.View/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.ViewCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.ViewModel/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.ViewModelCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "DevExpress.XtraReports.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.DataAccess.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Printing.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Data.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.XtraCharts.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Xpo.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Drawing.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Sparkline.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.PivotGrid.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Charts.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.XtraGauges.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Pdf.v24.1.Drawing/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Pdf.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.RichEdit.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Office.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.CodeParser.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.DataVisualization.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}}}