﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    <system:String x:Key="CompanyTitle">INC</system:String>
    <system:String x:Key="Exit">Exit</system:String>
    <system:String x:Key="Save">Save</system:String>
    <system:String x:Key="Width">Width</system:String>
    <system:String x:Key="ChangeOver">ChangeOver</system:String>
    <system:String x:Key="Complete">Complete</system:String>
    <system:String x:Key="AlignSwitchLiner">Align / SwitchLiner</system:String>
    <system:String x:Key="ApplicationAlreadyStarted">The program is already running and cannot be launched again.</system:String>
    <system:String x:Key="AdjustmentHistory">Measurement History</system:String>
    <system:String x:Key="ProductChangeOver">Product ChangeOver</system:String>
    <system:String x:Key="Add">Add</system:String>
    <system:String x:Key="Delete">Delete</system:String>
    <system:String x:Key="Edit">Edit</system:String>
    <system:String x:Key="Confirm">Confirm</system:String>
    <system:String x:Key="Cancel">Cancel</system:String>
    <system:String x:Key="Line">Line</system:String>
    <system:String x:Key="Status">Status</system:String>
    <system:String x:Key="MotionControlCardConnectFailed">The motion control card did not connect successfully! The current configuration for the local IP is {0}. The current configuration for the motion control card IP is {1}。</system:String>
    <system:String x:Key="DeviceStatus">Device Status</system:String>

    <system:String x:Key="DeviceStatus.Connected">Running</system:String>
    <system:String x:Key="DeviceStatus.DisConnected">DisConnected</system:String>

    <system:String x:Key="DurationFormatStringSecond">{0}S</system:String>
    <system:String x:Key="DurationFormatStringMinuteSecond">{0}Min{1}S</system:String>
    <system:String x:Key="DurationFormatStringHourMinuteSecond">{0}H{1}Min{2}S</system:String>
    <system:String x:Key="DurationFormatStringDayHourMinuteSecond">{0}D{1}H{2}Min{3}S</system:String>
    <system:String x:Key="InputProductName">Input Product Name</system:String>
    <system:String x:Key="InputProductWidth">Input Product Width</system:String>
    <system:String x:Key="ConfirmDeleteProduct">Confirm to delete product?</system:String>

    <system:String x:Key="MeasurementTime">Measurement Time</system:String>
    <system:String x:Key="MeasurementResult">Measurement Result</system:String>
    <system:String x:Key="UpperEdge">Upper Edge</system:String>
    <system:String x:Key="LowerEdge">Lower Edge</system:String>
    <system:String x:Key="Quality">Quality</system:String>
    <system:String x:Key="MeasurementLocation">Measurement Description</system:String>
    <system:String x:Key="UpperEdgeStatus">UpperEdge Status</system:String>
    <system:String x:Key="LowerEdgeStatus">LowerEdge Status</system:String>

    <system:String x:Key="Calibration">Calibration</system:String>

    <system:String x:Key="Start">Start</system:String>
    <system:String x:Key="ReSet">Reset</system:String>

    <system:String x:Key="MoveAxis">MoveAxis</system:String>
    <system:String x:Key="Name">Name</system:String>
    <system:String x:Key="Number">Number</system:String>
    <system:String x:Key="Origin">Origin</system:String>
    <system:String x:Key="CameraAxis">CameraAxis</system:String>
    <system:String x:Key="HorizontallyShoot">Horizontal Shoot</system:String>
    <system:String x:Key="VerticalShoot">Vertical Shoot</system:String>
    <system:String x:Key="TakePhoto">TakePicture</system:String>
    <system:String x:Key="CurrentMeter">Current Meter</system:String>
    <system:String x:Key="TakePhotoTime">Take Photo Time</system:String>

    <system:String x:Key="CurrentProduct">Current Product</system:String>

    <system:String x:Key="ProductDetail">Product Detail</system:String>
    <system:String x:Key="CreationDate">Creation Date</system:String>
    <system:String x:Key="ProductName">Product Name</system:String>
    <system:String x:Key="ProductHeight">Product Height</system:String>
    <system:String x:Key="CompletionNo">Completion No</system:String>
    <system:String x:Key="Operator">Operator</system:String>
</ResourceDictionary>