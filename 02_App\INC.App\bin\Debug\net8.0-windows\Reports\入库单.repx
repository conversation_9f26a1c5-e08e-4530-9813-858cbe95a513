﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="入库单" Margins="31, 33, 16, 16" PaperKind="A4" PageWidth="827" PageHeight="1169" Version="24.1" DataMember="Rep_Label_GetChinaStockInHeader" DataSource="#Ref-0" Font="宋体, 10pt">
  <Parameters>
    <Item1 Ref="3" Description="Parameter" Name="Parameter" />
  </Parameters>
  <Bands>
    <Item1 Ref="4" ControlType="DetailBand" Name="Detail" HeightF="0" Font="微软雅黑, 12pt">
      <StylePriority Ref="5" UseFont="false" />
    </Item1>
    <Item2 Ref="6" ControlType="TopMarginBand" Name="TopMargin" HeightF="16" />
    <Item3 Ref="7" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="16" />
    <Item4 Ref="8" ControlType="DetailReportBand" Name="DetailReport1" Level="0" DataMember="Rep_Label_GetChinaStockInBody" DataSource="#Ref-0" Font="微软雅黑, 12pt">
      <Bands>
        <Item1 Ref="9" ControlType="DetailBand" Name="Detail1" HeightF="45.01938">
          <Controls>
            <Item1 Ref="10" ControlType="XRTable" Name="table4" TextAlignment="MiddleCenter" SizeF="762,45.01938" LocationFloat="0,0" Borders="All">
              <Rows>
                <Item1 Ref="11" ControlType="XRTableRow" Name="tableRow5" Weight="1">
                  <Cells>
                    <Item1 Ref="12" ControlType="XRTableCell" Name="tableCell23" Weight="0.5442087040580967" Padding="2,2,0,0,96">
                      <ExpressionBindings>
                        <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[ItemType]" />
                      </ExpressionBindings>
                    </Item1>
                    <Item2 Ref="14" ControlType="XRTableCell" Name="tableCell26" Weight="0.48695311444248995" Text="tableCell26" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[ItemName]" />
                      </ExpressionBindings>
                    </Item2>
                    <Item3 Ref="16" ControlType="XRTableCell" Name="tableCell28" Weight="0.48695311444248995" Text="tableCell28" Padding="2,2,0,0,100">
                      <ExpressionBindings>
                        <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" />
                        <Item2 Ref="18" EventName="BeforePrint" PropertyName="Text" />
                        <Item3 Ref="19" EventName="BeforePrint" PropertyName="Text" />
                        <Item4 Ref="20" EventName="BeforePrint" PropertyName="Text" />
                        <Item5 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[Color]" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="22" ControlType="XRTableCell" Name="tableCell24" Weight="0.5444995169559607" TextFormatString="{0:#,#}" Text="tableCell24" Padding="2,2,0,0,96">
                      <ExpressionBindings>
                        <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[Quantity]" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="24" ControlType="XRTableCell" Name="tableCell25" Weight="0.5972280637786065" Text="tableCell25" Padding="2,2,0,0,96">
                      <ExpressionBindings>
                        <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[PackageNo]" />
                      </ExpressionBindings>
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="26" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
        <Item2 Ref="27" ControlType="GroupHeaderBand" Name="GroupHeader1" RepeatEveryPage="true" HeightF="89.447556">
          <Controls>
            <Item1 Ref="28" ControlType="XRLabel" Name="label2" Multiline="true" TextAlignment="MiddleCenter" SizeF="762,45.01938" LocationFloat="0,7.4014865E-15" Font="微软雅黑, 14pt" Padding="2,2,0,0,100" Borders="Left, Top, Right">
              <ExpressionBindings>
                <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[Rep_Label_GetChinaStockInHeader].[SalesOrderNo]" />
              </ExpressionBindings>
              <StylePriority Ref="30" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
            <Item2 Ref="31" ControlType="XRTable" Name="table3" TextAlignment="MiddleCenter" SizeF="761.9167,45.01938" LocationFloat="0,44.42818" Borders="All">
              <Rows>
                <Item1 Ref="32" ControlType="XRTableRow" Name="tableRow3" Weight="1">
                  <Cells>
                    <Item1 Ref="33" ControlType="XRTableCell" Name="tableCell11" Weight="0.9071136898004825" RowSpan="2" Padding="2,2,0,0,96">
                      <ExpressionBindings>
                        <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Rep_Label_GetChinaStockInHeader].[ItemType]" />
                      </ExpressionBindings>
                    </Item1>
                    <Item2 Ref="35" ControlType="XRTableCell" Name="tableCell17" Weight="0.811677272171317" RowSpan="2" Text="尺       寸">
                      <ExpressionBindings>
                        <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[Rep_Label_GetChinaStockInHeader].[ItemName]" />
                      </ExpressionBindings>
                    </Item2>
                    <Item3 Ref="37" ControlType="XRTableCell" Name="tableCell19" Weight="0.811677272171317" RowSpan="2" Text="数        量">
                      <ExpressionBindings>
                        <Item1 Ref="38" EventName="BeforePrint" PropertyName="Text" Expression="[Rep_Label_GetChinaStockInHeader].[Color]" />
                      </ExpressionBindings>
                    </Item3>
                    <Item4 Ref="39" ControlType="XRTableCell" Name="tableCell12" Weight="0.9075984309647733" RowSpan="2" Text="原料代号" Padding="2,2,0,0,96">
                      <ExpressionBindings>
                        <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[Rep_Label_GetChinaStockInHeader].[Quantity]" />
                      </ExpressionBindings>
                    </Item4>
                    <Item5 Ref="41" ControlType="XRTableCell" Name="tableCell13" Weight="0.9950042110461738" RowSpan="2" Text="包  装  指  示" Padding="2,2,0,0,96" Borders="All">
                      <ExpressionBindings>
                        <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[Rep_Label_GetChinaStockInHeader].[PackageNo]" />
                      </ExpressionBindings>
                      <StylePriority Ref="43" UseBorders="false" />
                    </Item5>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="44" UseBorders="false" UseTextAlignment="false" />
            </Item2>
          </Controls>
        </Item2>
        <Item3 Ref="45" ControlType="ReportFooterBand" Name="ReportFooter1" HeightF="45.01938">
          <Controls>
            <Item1 Ref="46" ControlType="XRLabel" Name="label3" Multiline="true" Text="label3" SizeF="100,23" LocationFloat="0,22.019379" Padding="2,2,0,0,100">
              <ExpressionBindings>
                <Item1 Ref="47" EventName="BeforePrint" PropertyName="Text" Expression="[Rep_Label_GetChinaStockInHeader].[BatchNo]" />
              </ExpressionBindings>
            </Item1>
            <Item2 Ref="48" ControlType="XRLabel" Name="label1" Multiline="true" Text="吴  江  聚  辉" TextAlignment="MiddleCenter" SizeF="139.50385,45.01938" LocationFloat="622.41284,0" Font="微软雅黑, 14pt, style=Bold" Padding="2,2,0,0,100">
              <StylePriority Ref="49" UseFont="false" UseTextAlignment="false" />
            </Item2>
          </Controls>
        </Item3>
      </Bands>
    </Item4>
  </Bands>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v24.1" Name="sqlDataSource1" Base64="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
  </ComponentStorage>
</XtraReportsLayoutSerializer>