﻿<syncfusion:ChromelessWindow x:Class="INC.TestApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:INC.TestApp"
        xmlns:regions="http://prismlibrary.com/"
        xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
        xmlns:viewModel="clr-namespace:INC.ViewModel;assembly=INC.ViewModel"
        xmlns:constants="clr-namespace:INC.ViewModel.Constants;assembly=INC.ViewModel"
        d:DataContext="{d:DesignInstance viewModel:MainWindowViewModel}"
        mc:Ignorable="d"
        WindowState="Maximized"
        WindowStyle="SingleBorderWindow"
        Title="MainWindow" 
        TitleBarHeight="0"
        Height="450" 
        Width="800"
        Icon="logo.ico">
    <syncfusion:ChromelessWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </syncfusion:ChromelessWindow.Resources>
    <syncfusion:SfBusyIndicator
        
        AnimationType="Cupertino"
        IsBusy="{Binding IsBusy}"
        Header="{Binding Header}"
        ViewboxHeight="80"
        ViewboxWidth="80">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="2"/>
                <RowDefinition Height="*"/>
                <!--<RowDefinition Height="5"/>
                <RowDefinition Height="50"/>-->
                <RowDefinition Height="0"/>
                <RowDefinition Height="0"/>
                <RowDefinition Height="2"/>
            </Grid.RowDefinitions>


            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="2"/>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="5"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="0"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2"/>
                    <ColumnDefinition Width="{Binding Width}"/>
                    <ColumnDefinition Width="5"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2"/>
                </Grid.ColumnDefinitions>

                <!--左上角, APP 名称-->
                <ContentControl 
                    Grid.Row="1" 
                    Grid.Column="1"
                    regions:RegionManager.RegionName="{x:Static constants:RegionNames.AppTitleView}" />

                <Grid Grid.Row="2"  Grid.Column="0" ColumnSpan="5" Background="#eaf3f9" />
                <!--左侧, Menu 菜单-->
                <ContentControl 
                    Grid.Row="3" 
                    Grid.Column="1"
                    regions:RegionManager.RegionName="{x:Static constants:RegionNames.MenuView}" />

                <!--右上角, 功能按钮-->
                <ContentControl 
                    Grid.Row="1" 
                    Grid.Column="3"
                    regions:RegionManager.RegionName="{x:Static constants:RegionNames.HeaderFunctionView}" />
                <Grid Grid.Row="3"  Grid.Column="2" Background="#eaf3f9" />

                <!--中间部分,主屏幕页面-->
                <ContentControl 
                    Grid.Row="3" 
                    Grid.Column="3"
                    regions:RegionManager.RegionName="{x:Static constants:RegionNames.MainPageView}" />
            </Grid>

            <Grid Grid.Row="2"  Grid.Column="0" Background="#eaf3f9" />

            <!--状态栏-->
            <ContentControl 
                Grid.Row="3" 
                regions:RegionManager.RegionName="{x:Static constants:RegionNames.StatusBarView}" />
        </Grid>
    </syncfusion:SfBusyIndicator>
    
</syncfusion:ChromelessWindow>
