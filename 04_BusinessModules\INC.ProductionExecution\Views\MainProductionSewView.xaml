<UserControl x:Class="INC.Production.Views.MainProductionSewView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.Production.Views"
             xmlns:viewModels="clr-namespace:INC.Production.ViewModels"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:constants="clr-namespace:INC.ProductionExecution.Models.Constants"
             xmlns:converters="clr-namespace:INC.ProductionSew.Views.Converters"
             d:DataContext="{d:DesignInstance viewModels:MainProductionSewViewModel}"
             mc:Ignorable="d" 
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <converters:BoolToColorBrushConverter x:Key="BoolToColorBrushConverter"/>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="5"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="5"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Background="#EFEFEF "/>
        <Grid Grid.Row="2" Background="#EFEFEF"/>
        <Grid Grid.Row="1" Background="White">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="10,0">
                <Image Source="/INC.Production;component/Resources/search.png"  Margin="5,10"/>
                <TextBlock Text="查询:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="20" Foreground="#cc333333" />
            </StackPanel>
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <TextBlock Text="工单号码:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                <TextBox  MinWidth="200" Text="{Binding ShopOrderNo,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}" 
                          VerticalAlignment="Center" VerticalContentAlignment="Center" BorderBrush="#4D333333" BorderThickness="1" FontSize="18" Margin="5,0" Padding="5,0" Height="35"/>
            </StackPanel>
            <StackPanel Grid.Column="3" Orientation="Horizontal">
                <TextBlock Text="产品名称:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                <TextBox  MinWidth="200" Text="{Binding ItemName,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}"
                          VerticalAlignment="Center" VerticalContentAlignment="Center" BorderBrush="#4D333333" BorderThickness="1" FontSize="18" Margin="5,0" Padding="5,0" Height="35"/>
            </StackPanel>
            <StackPanel Grid.Column="4" Orientation="Horizontal">
                <TextBlock Text="产品号:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                <TextBox  MinWidth="200" Text="{Binding ItemNo,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}"
                          VerticalAlignment="Center" VerticalContentAlignment="Center" BorderBrush="#4D333333" BorderThickness="1" FontSize="18" Margin="5,0" Padding="5,0" Height="35"/>
            </StackPanel>
            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <TextBlock Text="订单号:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                <TextBox  MinWidth="200" Text="{Binding SalesOrderNo,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}"
                          VerticalAlignment="Center" VerticalContentAlignment="Center" BorderBrush="#4D333333" BorderThickness="1" FontSize="18" Margin="5,0" Padding="5,0" Height="35"/>
            </StackPanel>
            <Button Grid.Column="5" Margin="5,0" HorizontalAlignment="Center" Height="40" Command="{Binding QueryCommand}">
                <Button.Template>
                    <ControlTemplate>
                        <Border BorderBrush="#0003B0 " CornerRadius="2"
                                BorderThickness="1" Background="#4DEAE6FF">
                            <StackPanel Orientation="Horizontal" Margin="35,0">
                                <Label
                                    VerticalContentAlignment="Center"
                                    Content="查询"
                                    FontSize="18"
                                    Foreground="#0003B0" />
                            </StackPanel>
                        </Border>
                    </ControlTemplate>
                </Button.Template>
            </Button>
        </Grid>
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="4*"/>
            </Grid.ColumnDefinitions>
            <GridSplitter Grid.Column="1"
                          ResizeDirection="Columns"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Stretch"
                          Width="5"
                          Background="White" />
            <Grid Grid.Column="0" Background="White" >
                <Grid.RowDefinitions>
                    <RowDefinition Height="55"/>
                    <RowDefinition Height="1"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Border Grid.Row="0" CornerRadius="2" Background="#F2F3F4 " Margin="2,2,15,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>

                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="选择" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                        <TextBlock Grid.Column="1" Text="工单号码" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                        <TextBlock Grid.Column="2" Text="产品名称" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                        <TextBlock Grid.Column="3" Text="客户" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                        <TextBlock Grid.Column="4" Text="数量" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                        <TextBlock Grid.Column="5" Text="颜色" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                        <TextBlock Grid.Column="6" Text="尺寸" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                        <TextBlock Grid.Column="7" Text="裁剪日期" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>

                    </Grid>
                </Border>
                <Grid Grid.Row="2">
                    <ScrollViewer
                        Margin="2,0,2,2"
                         Background="White"
                          HorizontalScrollBarVisibility="Disabled">
                        <ItemsControl ItemsSource="{Binding ShopOrders , UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <UniformGrid Columns="1" VerticalAlignment="Top" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border  CornerRadius="2" BorderThickness="0,0,0,1" BorderBrush="#F2F3F4 " Height="50"     MouseMove="UIElement_OnMouseMove" DragOver="UIElement_OnDragOver"
                                             Background="{Binding Select,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource BoolToColorBrushConverter},ConverterParameter=12}">
                                        <Border.InputBindings>
                                            <MouseBinding MouseAction="LeftClick" Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl, Mode=FindAncestor},Path=DataContext.SelectCommand}" CommandParameter="{Binding }"/>
                                            <MouseBinding MouseAction="LeftDoubleClick" Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl, Mode=FindAncestor},Path=DataContext.CancelSelectCommand}" CommandParameter="{Binding }"/>
                                        </Border.InputBindings>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="2*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.ToolTip>
                                                <Border
                                                    Margin="-6"
                                                    Background="#FFEC3D"
                                                    CornerRadius="4">
                                                    <Grid>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="10" />
                                                            <RowDefinition Height="*" />
                                                            <RowDefinition Height="5" />
                                                            <RowDefinition Height="*" />
                                                            <RowDefinition Height="5" />
                                                            <RowDefinition Height="*" />
                                                            <RowDefinition Height="5" />
                                                            <RowDefinition Height="*" />
                                                            <RowDefinition Height="10" />
                                                        </Grid.RowDefinitions>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="10" />
                                                            <ColumnDefinition Width="*" />
                                                            <ColumnDefinition Width="10" />
                                                        </Grid.ColumnDefinitions>
                                                        <Grid Grid.Row="1" Grid.Column="1">
                                                            <TextBlock Grid.Row="0" Foreground="Black" FontSize="20">
                                                                <Run Text="工单号码:" />
                                                                <Run Text="{Binding ShopOrderNo}" />
                                                            </TextBlock>
                                                        </Grid>
                                                        <Grid Grid.Row="3" Grid.Column="1">
                                                            <TextBlock Grid.Row="0" Foreground="Black" FontSize="20">
                                                                <Run Text="产品名称:" />
                                                                <Run Text="{Binding ItemName}" />
                                                            </TextBlock>
                                                        </Grid>
                                                        <Grid Grid.Row="5" Grid.Column="1">
                                                            <TextBlock Grid.Row="0" Foreground="Black" FontSize="20">
                                                                <Run Text="客户:" />
                                                                <Run Text="{Binding CustomerName}" />
                                                            </TextBlock>
                                                        </Grid>
                                                        <Grid Grid.Row="7" Grid.Column="1">
                                                            <TextBlock Grid.Row="0" Foreground="Black" FontSize="20">
                                                                <Run Text="数量:" />
                                                                <Run Text="{Binding Quantity}" />
                                                            </TextBlock>
                                                        </Grid>
                                                    </Grid>
                                                </Border>
                                            </Grid.ToolTip>
                                            <CheckBox Grid.Column="0" IsChecked="{Binding Select,UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center" BorderBrush="Black">
                                                <CheckBox.LayoutTransform>
                                                    <ScaleTransform ScaleX="1.3" ScaleY="1.3" />
                                                </CheckBox.LayoutTransform>
                                            </CheckBox>
                                            <TextBlock Grid.Column="1" Text="{Binding ShopOrderNo}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                            <TextBlock Grid.Column="2" Text="{Binding ItemName}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                            <TextBlock Grid.Column="3" Text="{Binding CustomerName}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                            <TextBlock Grid.Column="4" Text="{Binding Quantity}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                            <TextBlock Grid.Column="5" Text="{Binding Color}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                            <TextBlock Grid.Column="6" Text="{Binding ItemType}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Grid>
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="5"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Grid Grid.Row="0" Background="White">
                    <ScrollViewer
                        Margin="5"
                        VerticalScrollBarVisibility="Disabled">
                        <ItemsControl ItemsSource="{Binding WorkShopModels , UpdateSourceTrigger=PropertyChanged}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <UniformGrid Rows="1" HorizontalAlignment="Left" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button Margin="5,0" HorizontalAlignment="Center"  Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl,Mode=FindAncestor},Path=DataContext.ChangeWorkPlaceCommand}" CommandParameter="{Binding}">
                                        <Button.Template>
                                            <ControlTemplate>
                                                <Border BorderBrush="Transparent " CornerRadius="4"
                                                        BorderThickness="1" Background="{Binding Select,Converter={StaticResource BoolToColorBrushConverter},ConverterParameter=2}">
                                                    <StackPanel Orientation="Horizontal" Margin="35,0">
                                                        <Label
                                                            VerticalContentAlignment="Center"
                                                            Content="{Binding WorkShop}"
                                                            FontSize="18"
                                                            Foreground="{Binding Select,Converter={StaticResource BoolToColorBrushConverter},ConverterParameter=1}" />
                                                    </StackPanel>
                                                </Border>
                                            </ControlTemplate>
                                        </Button.Template>
                                    </Button>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
                <Grid Grid.Row="1" Background="#EFEFEF"/>
                <ScrollViewer
                    Grid.Row="2"
                Margin="5,0,5,5"
                Background="#EFEFEF"
                 HorizontalScrollBarVisibility="Disabled">
                    <ItemsControl ItemsSource="{Binding WorkPlaces , UpdateSourceTrigger=PropertyChanged}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="4" VerticalAlignment="Top" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border  CornerRadius="4" BorderThickness="0" BorderBrush="Transparent" Height="210" 
                                         AllowDrop="True" Drop="UIElement_OnDrop" Margin="5,5" Background="White">
                                    <Border.InputBindings>
                                        <MouseBinding MouseAction="LeftDoubleClick" 
                                                      Command="{Binding  RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=UserControl},Path=DataContext.SewAssignmentCommand}"
                                                      CommandParameter="{Binding}"/>
                                    </Border.InputBindings>
                                    <Grid >
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="45"/>
                                            <RowDefinition Height="40"/>
                                            <RowDefinition Height="40"/>
                                            <RowDefinition Height="40"/>
                                            <RowDefinition Height="40"/>
                                            <RowDefinition Height="5"/>
                                        </Grid.RowDefinitions>
                                        <Grid Grid.Row="0" Background="{Binding IsCurrent,Converter={StaticResource BoolToColorBrushConverter},ConverterParameter=11}">
                                            <StackPanel Orientation="Horizontal" Margin="15,0">
                                                <Image Source="/INC.Production;component/Resources/旗标_白.png" Margin="0,10"/>
                                                <TextBlock Text="{Binding Description}" VerticalAlignment="Center" FontSize="20" Foreground="White" Margin="5,0"/>
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="15,0" HorizontalAlignment="Right">
                                                <Image Source="/INC.Production;component/Resources/person.png" Margin="0,8"/>
                                                <TextBlock Text="{Binding UserName}" VerticalAlignment="Center" FontSize="20" Foreground="White" Margin="5,0"/>
                                            </StackPanel>
                                        </Grid>
                                        <Border Grid.Row="1" BorderThickness="0,0,0,1" BorderBrush="#D8D8D8" Margin="20,0">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="2*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="工单号码：" VerticalAlignment="Center" FontSize="18" Foreground="#80333333"/>
                                                <TextBlock Grid.Column="1" Text="{Binding ShopOrderNo}" VerticalAlignment="Center" HorizontalAlignment="Right" FontSize="18" Foreground="#CC333333"/>
                                            </Grid>
                                        </Border>
                                        <Border Grid.Row="2" BorderThickness="0,0,0,1" BorderBrush="#D8D8D8" Margin="20,0">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="2*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="产品名称：" VerticalAlignment="Center" FontSize="18" Foreground="#80333333"/>
                                                <TextBlock Grid.Column="1" Text="{Binding ItemName}" VerticalAlignment="Center" HorizontalAlignment="Right" FontSize="18" Foreground="#CC333333"/>
                                            </Grid>
                                        </Border>
                                        <Border Grid.Row="3" BorderThickness="0,0,0,1" BorderBrush="#D8D8D8" Margin="20,0">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="2*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="产品号码：" VerticalAlignment="Center" FontSize="18" Foreground="#80333333"/>
                                                <TextBlock Grid.Column="1" Text="{Binding ItemNo}" VerticalAlignment="Center" HorizontalAlignment="Right" FontSize="18" Foreground="#CC333333"/>
                                            </Grid>
                                        </Border>
                                        <Grid Grid.Row="4"  Margin="20,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="2*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="工单数量：" VerticalAlignment="Center" FontSize="18" Foreground="#80333333"/>
                                            <TextBlock Grid.Column="1" Text="{Binding Quantity}" VerticalAlignment="Center" HorizontalAlignment="Right" FontSize="18" Foreground="#CC333333"/>
                                        </Grid>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
