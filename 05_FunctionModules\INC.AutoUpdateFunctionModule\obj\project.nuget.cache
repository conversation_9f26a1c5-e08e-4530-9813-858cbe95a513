{"version": 2, "dgSpecHash": "dB2PIGXwGCI=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\05_FunctionModules\\INC.AutoUpdateFunctionModule\\INC.AutoUpdateFunctionModule.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.1\\microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.container.abstractions\\9.0.106\\prism.container.abstractions.9.0.106.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.core\\9.0.537\\prism.core.9.0.537.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.events\\9.0.537\\prism.events.9.0.537.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.1.0\\serilog.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\6.0.0\\serilog.sinks.console.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\6.0.0\\serilog.sinks.file.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.compression.net.core\\27.2.4\\syncfusion.compression.net.core.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.licensing\\27.2.4\\syncfusion.licensing.27.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.xlsio.net.core\\27.2.4\\syncfusion.xlsio.net.core.27.2.4.nupkg.sha512"], "logs": []}