﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:system="clr-namespace:System;assembly=mscorlib">
    <system:String x:Key="CompanyTitle">云联智造</system:String>
    <system:String x:Key="Exit">退出</system:String>
    <system:String x:Key="Save">保存</system:String>
    <system:String x:Key="Width">宽度</system:String>
    <system:String x:Key="ChangeOver">产品换型</system:String>
    <system:String x:Key="Complete">完 成</system:String>
    <system:String x:Key="AlignSwitchLiner">校准 / 更换脱模布</system:String>
    <system:String x:Key="ApplicationAlreadyStarted">程序已经在运行，无法再次启动</system:String>
    <system:String x:Key="AdjustmentHistory">检测记录</system:String>
    <system:String x:Key="ProductChangeOver">产品换型</system:String>
    <system:String x:Key="Add">新增</system:String>
    <system:String x:Key="Delete">删除</system:String>
    <system:String x:Key="Edit">编辑</system:String>
    <system:String x:Key="Confirm">确认</system:String>
    <system:String x:Key="Cancel">取消</system:String>
    <system:String x:Key="Line">线别</system:String>
    <system:String x:Key="Status">状态</system:String>
    <system:String x:Key="DeviceStatus">设备状态</system:String>
    <system:String x:Key="DeviceStatus.Connected">运行中</system:String>
    <system:String x:Key="DeviceStatus.DisConnected">已断开</system:String>
    <system:String x:Key="DurationFormatStringSecond">{0}秒</system:String>
    <system:String x:Key="DurationFormatStringMinuteSecond">{0}分钟{1}秒</system:String>
    <system:String x:Key="DurationFormatStringHourMinuteSecond">{0}小时{1}分钟{2}秒</system:String>
    <system:String x:Key="DurationFormatStringDayHourMinuteSecond">{0}天{1}小时{2}分钟{3}秒</system:String>
    <system:String x:Key="InputProductName">请输入产品名称</system:String>
    <system:String x:Key="InputProductWidth">请输入产品宽度</system:String>
    <system:String x:Key="ConfirmDeleteProduct">确认删除产品?</system:String>

    <system:String x:Key="MeasurementTime">检测时间</system:String>
    <system:String x:Key="MeasurementResult">检测结果</system:String>
    <system:String x:Key="UpperEdge">上边缘</system:String>
    <system:String x:Key="LowerEdge">下边缘</system:String>
    <system:String x:Key="Quality">品质</system:String>
    <system:String x:Key="MeasurementLocation">检测位置</system:String>
    <system:String x:Key="UpperEdgeStatus">上边缘状态</system:String>
    <system:String x:Key="LowerEdgeStatus">下边缘状态</system:String>

    <system:String x:Key="Calibration">校准</system:String>

    <system:String x:Key="Start">开始</system:String>
    <system:String x:Key="ReSet">重置</system:String>

    <system:String x:Key="MoveAxis">运动轴</system:String>
    <system:String x:Key="Name">名称</system:String>
    <system:String x:Key="Number">编号</system:String>
    <system:String x:Key="Origin">回原点</system:String>
    <system:String x:Key="CameraAxis">相机轴</system:String>
    <system:String x:Key="HorizontallyShoot">水平拍摄</system:String>
    <system:String x:Key="VerticalShoot">垂直拍摄</system:String>
    <system:String x:Key="TakePhoto">拍照中</system:String>
    <system:String x:Key="CurrentMeter">当前米数</system:String>
    <system:String x:Key="TakePhotoTime">拍照时间</system:String>

    <system:String x:Key="CurrentProduct">当前产品</system:String>

    <system:String x:Key="ProductDetail">当日详情</system:String>
    <system:String x:Key="CreationDate">时间</system:String>
    <system:String x:Key="ProductName">产品名称</system:String>
    <system:String x:Key="ProductHeight">产品高度</system:String>
    <system:String x:Key="CompletionNo">完成序号</system:String>
    <system:String x:Key="Operator">操作人员</system:String>

</ResourceDictionary>