﻿using Serilog;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using INC.CompactFunctionModule.Log;
using INC.ViewModelCore.Interface;
using INC.ViewCore.Theme;
using System.ComponentModel;
using Syncfusion.Windows.Shared;
using INC.ViewModel.MainPage;
using INC.ViewModelCore.Message;

namespace INC.TestApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : ChromelessWindow
    {
        private readonly IThemeService _themeService;
        private readonly HeaderFunctionViewModel _headerFunctionViewModel;

        public MainWindow(IThemeService themeService,
            HeaderFunctionViewModel headerFunctionViewModel)
        {
            _themeService = themeService;
            _headerFunctionViewModel = headerFunctionViewModel;
            _headerFunctionViewModel.IsShowUserPanel = false;
            InitializeComponent();

            _themeService.SetCurrentTheme(this);

            MouseDown += (s, e) =>
            {
                if (e.ClickCount == 2) SetWindowState();
            };

            MouseMove += (s, e) =>
            {
                if (e.LeftButton == System.Windows.Input.MouseButtonState.Pressed)
                    this.DragMove();
            };

            _headerFunctionViewModel.Closed += _headerFunctionViewModel_Closed;
            _headerFunctionViewModel.Minimized += _headerFunctionViewModel_Minimized;
            _headerFunctionViewModel.Maximized += _headerFunctionViewModel_Maximized;
        }


        private void SetWindowState()
        {
            this.WindowState = ((base.WindowState != System.Windows.WindowState.Maximized)
                ? System.Windows.WindowState.Maximized
                : System.Windows.WindowState.Normal);
            SystemButtonsUpdate();
        }




        private void _headerFunctionViewModel_Minimized(object? sender, EventArgs e)
        {
            WindowState = ((base.WindowState != System.Windows.WindowState.Minimized)
                ? System.Windows.WindowState.Minimized
                : System.Windows.WindowState.Normal);

            //this.Hide();
        }

        private void _headerFunctionViewModel_Maximized(object? sender, EventArgs e)
        {
            SetWindowState();
        }

        private async void _headerFunctionViewModel_Closed(object? sender, EventArgs e)
        {
            var messageService = ContainerLocator.Container.Resolve<IMessageService>();
            if (await messageService.Confirm("确认退出?"))
            {
                Environment.Exit(-1);
            }
        }
    }
}