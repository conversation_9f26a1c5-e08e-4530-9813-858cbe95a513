﻿++解决方案 'JH.Desktop' ‎ (15 个项目，共 15 个)
i:{00000000-0000-0000-0000-000000000000}:JH.Desktop.sln
++02_App
i:{00000000-0000-0000-0000-000000000000}:02_App
++INC.App
i:{ce76c1cc-e04a-4905-b34b-c1931618ef1c}:INC.App
++依赖项
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3735
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3724
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>3736
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:>3726
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3731
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>3730
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3733
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3727
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>3725
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:>3737
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3734
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3728
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3723
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:>3732
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3729
++Language
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\language\
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\language\
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\model\language\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\language\
++Reports
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\reports\
++Resources
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\
++Splash
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\splash\
++App.xaml
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\app.xaml
++appsettings.json
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\appsettings.json
++AssemblyInfo.cs
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\assemblyinfo.cs
++INC.App.csproj.vspscc
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\inc.app.csproj.vspscc
++logo.ico
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\logo.ico
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\logo.ico
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\images\logo.ico
++MainWindow.xaml
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\mainwindow.xaml
++包
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3864
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3972
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4000
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3871
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>4002
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3852
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3798
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3979
++分析器
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3825
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3767
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>3930
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:>3742
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3905
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>3914
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3840
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3922
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>3927
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:>3876
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3760
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3822
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3752
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:>3885
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3915
++框架
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3859
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3813
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>3998
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:>3786
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3963
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>3986
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3867
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3988
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>3996
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:>3893
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3821
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3848
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3791
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:>3900
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3970
++项目
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3746
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3739
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>3903
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>3904
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3740
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3907
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>3906
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:>3738
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3745
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3743
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3741
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:>3744
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3902
++Chinese.xaml
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\language\chinese.xaml
++English.xaml
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\language\english.xaml
++工单标签.repx
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\reports\工单标签.repx
++入库单.repx
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\reports\入库单.repx
++箱麦二维码.repx
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\reports\箱麦二维码.repx
++英文入库单.repx
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\reports\英文入库单.repx
++Images
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\images\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\images\
++SplashScreen.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\splash\splashscreen.png
++App.xaml.cs
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\app.xaml.cs
++MainWindow.xaml.cs
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\mainwindow.xaml.cs
++Microsoft.Extensions.Configuration.Abstractions (9.0.0)
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3866
++Microsoft.Extensions.Configuration.Binder (9.0.0)
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3868
++Microsoft.Extensions.Configuration.FileExtensions (9.0.0)
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3870
++Microsoft.Extensions.Configuration.Json (9.0.0)
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3872
++CommunityToolkit.Mvvm.CodeFixers
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
++CommunityToolkit.Mvvm.SourceGenerators
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.0\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3862
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3819
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>4006
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:>3793
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3969
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>3995
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3869
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3997
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>4009
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:>3896
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3827
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3851
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3794
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:>3901
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3976
++Microsoft.WindowsDesktop.App.WPF
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3861
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3817
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>4003
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>3991
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3993
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>4001
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:>3894
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3824
++INC.AutoUpdateFunctionModule
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3805
i:{bdad66ae-3401-44f4-9fdf-adecc5a94933}:INC.AutoUpdateFunctionModule
++INC.BusinessModuleCore
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3756
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3748
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3912
i:{720aa717-3a35-496d-a27a-6c82e78ca515}:INC.BusinessModuleCore
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:>3875
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3747
++INC.Common
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3777
i:{ef8b7da4-c814-437f-9f0f-017e52f035c4}:INC.Common
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3837
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3913
++INC.CompactFunctionModule
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3751
i:{bdad66ae-3401-44f4-9fdf-adecc5a94933}:INC.CompactFunctionModule
++INC.DeviceFunctionModule
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3771
i:{bdad66ae-3401-44f4-9fdf-adecc5a94933}:INC.DeviceFunctionModule
++INC.FunctionModuleCore
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3763
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>3911
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3818
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3749
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:>3883
i:{bdad66ae-3401-44f4-9fdf-adecc5a94933}:INC.FunctionModuleCore
++INC.HalfScrap
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3783
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3755
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>3920
i:{720aa717-3a35-496d-a27a-6c82e78ca515}:INC.HalfScrap
++INC.LoginBusinessModule
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3823
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3766
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>3916
i:{720aa717-3a35-496d-a27a-6c82e78ca515}:INC.LoginBusinessModule
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:>3757
++INC.Production
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3800
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:>3761
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>3926
i:{720aa717-3a35-496d-a27a-6c82e78ca515}:INC.Production
++INC.View
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3796
i:{bdd7e960-8aa5-4b60-ab48-532ba5198510}:INC.View
++INC.ViewCore
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3811
i:{ef8b7da4-c814-437f-9f0f-017e52f035c4}:INC.ViewCore
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>3918
++INC.ViewModel
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3790
i:{bdd7e960-8aa5-4b60-ab48-532ba5198510}:INC.ViewModel
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:>3909
++INC.ViewModelCore
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:>3815
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>3908
i:{ef8b7da4-c814-437f-9f0f-017e52f035c4}:INC.ViewModelCore
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:>3924
++menus
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\
++accounts.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\accounts.png
++download.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\download.png
++logout.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\logout.png
++mysettings.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\mysettings.png
++password.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\password.png
++role.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\role.png
++setting.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\setting.png
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\setting.png
++tenant.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\tenant.png
++user.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\user.png
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\user.png
++包装_白.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\包装_白.png
++包装_黑.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\包装_黑.png
++报废_白.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\报废_白.png
++报废_包装白.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\报废_包装白.png
++报废_包装黑.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\报废_包装黑.png
++报废_黑.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\报废_黑.png
++缝纫_白.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\缝纫_白.png
++缝纫_黑.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\缝纫_黑.png
++未下发_白.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\未下发_白.png
++未下发_黑.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\未下发_黑.png
++已下发_白.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\已下发_白.png
++已下发_黑.png
i:{e01715fa-1011-4685-9b6d-14c042c651ce}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\02_app\inc.app\resources\images\menus\已下发_黑.png
++03_Desktop
i:{00000000-0000-0000-0000-000000000000}:03_Desktop
++Constants
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\constants\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\models\constants\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\models\constants\
++Interface
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\interface\
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\interface\
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\interface\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\interface\
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\interface\
++MainPage
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\
++Manager
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\manager\
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\manager\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\manager\
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\manager\
++Menu
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\menu\
++Model
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\model\
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\model\
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\model\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\model\
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\
++ViewModels
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\viewmodels\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\viewmodels\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\
++INC.ViewModel.csproj.vspscc
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\inc.viewmodel.csproj.vspscc
++MainWindowViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainwindowviewmodel.cs
++PermissionNames.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\constants\permissionnames.cs
++RegionNames.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\constants\regionnames.cs
++ResourcesKeys.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\constants\resourceskeys.cs
++IMenuManager.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\interface\imenumanager.cs
++IViewModelFactory.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\interface\iviewmodelfactory.cs
++User
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\user\
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\user\
++AppTitleViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\apptitleviewmodel.cs
++HeaderFunctionViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\headerfunctionviewmodel.cs
++MainPageViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\mainpageviewmodel.cs
++MenuViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\menuviewmodel.cs
++StatusBarViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\statusbarviewmodel.cs
++NavigationManager.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\manager\navigationmanager.cs
++NotificationManager.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\manager\notificationmanager.cs
++ViewModelFactory.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\manager\viewmodelfactory.cs
++MenuItem.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\menu\menuitem.cs
++MenuItemViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\menu\menuitemviewmodel.cs
++MenuManager.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\menu\menumanager.cs
++PageMenuConfiguration.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\menu\pagemenuconfiguration.cs
++DeviceStatus.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\model\devicestatus.cs
++ParticleMixing1ViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\viewmodels\particlemixing1viewmodel.cs
++ParticleMixing2ViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\viewmodels\particlemixing2viewmodel.cs
++Production1ViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\viewmodels\production1viewmodel.cs
++Production2ViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\viewmodels\production2viewmodel.cs
++NotificationMessage.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\user\notificationmessage.cs
++PermissionItemViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\user\permissionitemviewmodel.cs
++UserPanelViewModel.cs
i:{58c168b0-8389-4769-915e-1fea2eae54e1}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.viewmodel\mainpage\user\userpanelviewmodel.cs
++Converters
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\converters\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\converters\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\converters\
++Views
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\views\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\
++ParticleMixing1View.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\particlemixing1view.xaml
++ParticleMixing2View.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\particlemixing2view.xaml
++Production1View.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\production1view.xaml
++Production2View.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\production2view.xaml
++INC.View.csproj.vspscc
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\inc.view.csproj.vspscc
++INCViewModule.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\incviewmodule.cs
++DeviceStatusToColorConverter.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\converters\devicestatustocolorconverter.cs
++MenuImageConverter.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\converters\menuimageconverter.cs
++MenuTitleConverter.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\converters\menutitleconverter.cs
++AppTitleView.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\apptitleview.xaml
++HeaderFunctionView.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\headerfunctionview.xaml
++MainPageView.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\mainpageview.xaml
++MenuView.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\menuview.xaml
++StatusBarView.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\statusbarview.xaml
++Fonts
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\fonts\
++FontIcons.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\fonticons.xaml
++ViewBorderDictionary.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\viewborderdictionary.xaml
++ViewButtonDictionary.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\viewbuttondictionary.xaml
++ViewListViewDictionary.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\viewlistviewdictionary.xaml
++ViewToggleButtonDictionary.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\viewtogglebuttondictionary.xaml
++ParticleMixing1View.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\particlemixing1view.xaml.cs
++ParticleMixing2View.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\particlemixing2view.xaml.cs
++Production1View.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\production1view.xaml.cs
++Production2View.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\views\production2view.xaml.cs
++UserPanelView.xaml
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\user\userpanelview.xaml
++AppTitleView.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\apptitleview.xaml.cs
++HeaderFunctionView.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\headerfunctionview.xaml.cs
++MainPageView.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\mainpageview.xaml.cs
++MenuView.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\menuview.xaml.cs
++StatusBarView.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\statusbarview.xaml.cs
++iconfont.ttf
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\fonts\iconfont.ttf
++info.png
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\info.png
++logo.png
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\logo.png
++logout.ico
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\logout.ico
++message.png
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\message.png
++notification.png
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\resources\images\notification.png
++UserPanelView.xaml.cs
i:{3f9bef65-7e05-46a9-a079-4d0541067bd5}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\03_desktop\inc.view\mainpage\user\userpanelview.xaml.cs
++01_Doc
i:{00000000-0000-0000-0000-000000000000}:01_Doc
++Doc
i:{d1961713-b8ae-494c-a486-5047075c4a6c}:Doc
++需求
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\01_doc\doc\需求\
++页面布局
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\01_doc\doc\页面布局\
++Doc.csproj.vspscc
i:{41f73e3e-15a1-450b-a6d0-3693358ca306}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\01_doc\doc\doc.csproj.vspscc
++06_Infrustructures
i:{00000000-0000-0000-0000-000000000000}:06_Infrustructures
++Common
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\common\
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\common\
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\common\
++Static
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\
++Utility
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\utility\
++INC.Common.csproj.vspscc
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\inc.common.csproj.vspscc
++程序集
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3999
++AppSettingsManager.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\common\appsettingsmanager.cs
++ServiceClientBase.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\common\serviceclientbase.cs
++Application
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\interface\application\
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\utility\application\
++IPrintManager.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\interface\iprintmanager.cs
++IServiceClient.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\interface\iserviceclient.cs
++ISimpleAesHelper.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\interface\isimpleaeshelper.cs
++Settings
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\model\settings\
++OperationResult.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\model\operationresult.cs
++OptionsBase.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\model\optionsbase.cs
++Extension
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\
++Http
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\http\
++Json
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\json\
++Security
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\security\
++CommonConstants.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\utility\commonconstants.cs
++PrintManager.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\utility\printmanager.cs
++ServiceClient.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\utility\serviceclient.cs
++SimpleAesHelper.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\utility\simpleaeshelper.cs
++Newtonsoft.Json (13.0.1)
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3977
++Serilog (4.1.0)
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3994
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3820
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>4039
++Serilog.Sinks.Console (6.0.0)
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3982
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3809
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3990
++Serilog.Sinks.File (6.0.0)
i:{c31ed1e1-e321-4193-962a-554209302f72}:>3987
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3816
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>4038
++DevExpress.Charts.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4035
++DevExpress.CodeParser.v24.1
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4036
++DevExpress.Data.v24.1
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4023
++DevExpress.DataAccess.v24.1
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4021
++DevExpress.DataVisualization.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4008
++DevExpress.Drawing.v24.1
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4029
++DevExpress.Office.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4012
++DevExpress.Pdf.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4020
++DevExpress.Pdf.v24.1.Drawing
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4027
++DevExpress.PivotGrid.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4025
++DevExpress.Printing.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4004
++DevExpress.RichEdit.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4018
++DevExpress.RichEdit.v24.1.Export
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4037
++DevExpress.Sparkline.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4034
++DevExpress.Xpo.v24.1
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4031
++DevExpress.XtraCharts.v24.1
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4013
++DevExpress.XtraGauges.v24.1.Core
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4033
++DevExpress.XtraReports.v24.1
i:{c31ed1e1-e321-4193-962a-554209302f72}:>4016
++ITimerProvider.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\interface\application\itimerprovider.cs
++AppSettingModel.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\model\settings\appsettingmodel.cs
++ByteExtension.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\byteextension.cs
++DateTimeExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\datetimeextensionmethods.cs
++DoubleExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\doubleextensionmethods.cs
++EnumerableExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\enumerableextensionmethods.cs
++EnumExtension.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\enumextension.cs
++FileHelper.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\filehelper.cs
++PropertyInfoExtension.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\propertyinfoextension.cs
++SingleExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\singleextensionmethods.cs
++StreamExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\streamextensionmethods.cs
++StringExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\stringextensionmethods.cs
++TimeExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\extension\timeextensionmethods.cs
++IncHttpUtility.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\http\inchttputility.cs
++JsonExtension.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\json\jsonextension.cs
++CryptoHelper.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\security\cryptohelper.cs
++PasswordExtensionMethods.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\static\security\passwordextensionmethods.cs
++TimerProvider.cs
i:{c31ed1e1-e321-4193-962a-554209302f72}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.common\utility\application\timerprovider.cs
++AttachedProperty
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\attachedproperty\
++Converter
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\
++Dialog
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\dialog\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\dialog\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\dialog\
++Localize
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\localize\
++Message
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\message\
++Theme
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\theme\
++INC.ViewCore.csproj.vspscc
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\inc.viewcore.csproj.vspscc
++Marshal.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\marshal.cs
++ViewBase.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\viewbase.cs
++ViewCoreModule.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\viewcoremodule.cs
++PermissionManager.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\attachedproperty\permissionmanager.cs
++BooleanToVisibilityConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\booleantovisibilityconverter.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\converters\booleantovisibilityconverter.cs
++BoolInvertConverter .cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\boolinvertconverter .cs
++ColorToBrushConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\colortobrushconverter.cs
++GridColumnSizerExt.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\gridcolumnsizerext.cs
++GridWidthConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\gridwidthconverter.cs
++InversionBooleanToVisibilityConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\inversionbooleantovisibilityconverter.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\converters\inversionbooleantovisibilityconverter.cs
++LanguageImageConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\languageimageconverter.cs
++LanguageStringConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\languagestringconverter.cs
++StarRatio.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\starratio.cs
++StringNameToImageConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\stringnametoimageconverter.cs
++ZeroToNAConverter.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\converter\zerotonaconverter.cs
++CustomDialogWindow.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\dialog\customdialogwindow.xaml
++ViewCoreChinese.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\language\viewcorechinese.xaml
++ViewCoreEnglish.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\language\viewcoreenglish.xaml
++Local.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\localize\local.cs
++LocalizeOptions.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\localize\localizeoptions.cs
++Localizer.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\localize\localizer.cs
++TranslateExtension.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\localize\translateextension.cs
++MessageConfirmView.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\messageconfirmview.xaml
++MessageService.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\messageservice.cs
++MessageShowAutoCloseView.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\messageshowautocloseview.xaml
++MessageShowView.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\messageshowview.xaml
++ViewCoreBorderDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\viewcoreborderdictionary.xaml
++ViewCoreButtonDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\viewcorebuttondictionary.xaml
++ViewCoreImageDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\viewcoreimagedictionary.xaml
++ViewCoreTextBlockDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\viewcoretextblockdictionary.xaml
++ViewCoreUserControlDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\viewcoreusercontroldictionary.xaml
++ViewCoreWindowDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\viewcorewindowdictionary.xaml
++SkinManagerExtensions.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\theme\skinmanagerextensions.cs
++ThemeCategory.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\theme\themecategory.cs
++ThemeOptions.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\theme\themeoptions.cs
++ThemeService.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\theme\themeservice.cs
++Microsoft.Xaml.Behaviors.Wpf (1.1.135)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4015
++Prism.Core (9.0.537)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4022
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3874
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>4040
++Prism.Unity (9.0.537)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4024
++Syncfusion.Licensing (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4011
++Syncfusion.SfBusyIndicator.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4030
++Syncfusion.SfGrid.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4010
++Syncfusion.SfSkinManager.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4017
++Syncfusion.SfTreeView.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4019
++Syncfusion.Themes.MaterialDark.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4028
++Syncfusion.Themes.MaterialDarkBlue.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4026
++Syncfusion.Themes.MaterialLight.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4005
++Syncfusion.Themes.MaterialLightBlue.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4014
++Syncfusion.Tools.WPF (27.2.4)
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:>4032
++CustomDialogWindow.xaml.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\dialog\customdialogwindow.xaml.cs
++MessageConfirmView.xaml.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\messageconfirmview.xaml.cs
++MessageShowAutoCloseView.xaml.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\messageshowautocloseview.xaml.cs
++MessageShowView.xaml.cs
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\message\messageshowview.xaml.cs
++KnownColorDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\common\knowncolordictionary.xaml
++ViewCoreColorDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\common\viewcorecolordictionary.xaml
++ViewCoreFontDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\common\viewcorefontdictionary.xaml
++ViewCoreSizeDictionary.xaml
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\common\viewcoresizedictionary.xaml
++Information.png
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\images\information.png
++Warning.png
i:{d39a1ca8-2c9c-4d8d-ac8e-b68d68b8673b}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewcore\resources\images\warning.png
++Keys
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\keys\
++DialogViewModelBase.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\dialogviewmodelbase.cs
++INC.ViewModelCore.csproj.vspscc
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\inc.viewmodelcore.csproj.vspscc
++NavigationViewModelBase.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\navigationviewmodelbase.cs
++ViewModelBase.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\viewmodelbase.cs
++IBusyIndicatorNotify.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\interface\ibusyindicatornotify.cs
++ILoadedAware.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\interface\iloadedaware.cs
++ILocalizer.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\interface\ilocalizer.cs
++IMarshal.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\interface\imarshal.cs
++IThemeService.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\interface\ithemeservice.cs
++CoreResourceKeys.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\keys\coreresourcekeys.cs
++BusyIndicatorNotify.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\manager\busyindicatornotify.cs
++RegionNotificationManager.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\manager\regionnotificationmanager.cs
++IMessageService.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\message\imessageservice.cs
++MessageConfirmViewModel.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\message\messageconfirmviewmodel.cs
++MessageShowAutoCloseViewModel.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\message\messageshowautocloseviewmodel.cs
++MessageShowViewModel.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\message\messageshowviewmodel.cs
++BusyIndicatorModel.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\model\busyindicatormodel.cs
++CommunityToolkit.Mvvm (8.3.2)
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:>3873
++Language.cs
i:{be9376e3-74d9-4058-a8f8-d1fa018e920e}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\06_infrustructures\inc.viewmodelcore\model\language\language.cs
++04_BusinessModules
i:{00000000-0000-0000-0000-000000000000}:04_BusinessModules
++Models
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\models\
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\models\
++HeaderFoot
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\headerfoot\
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\headerfoot\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\headerfoot\
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\
++MainIssueShopOrderViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\mainissueshoporderviewmodel.cs
++MainPackageViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\mainpackageviewmodel.cs
++MainProductionSewViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\mainproductionsewviewmodel.cs
++MainUnIssueShopOrderViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\mainunissueshoporderviewmodel.cs
++BatchIssueShopOrderView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\batchissueshoporderview.xaml
++ChangeRouteView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\changerouteview.xaml
++CorrectView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\correctview.xaml
++DivideView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\divideview.xaml
++InStorageView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\instorageview.xaml
++InventoryLessView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\inventorylessview.xaml
++IssueShopOrderView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\issueshoporderview.xaml
++PackageView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\packageview.xaml
++PackageView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\packageview.xaml.cs
++ReallocateView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\reallocateview.xaml
++ScrapView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\scrapview.xaml
++SewAssignmentView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\sewassignmentview.xaml
++ShopOrderDivideView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\shoporderdivideview.xaml
++MainIssueShopOrderView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainissueshoporderview.xaml
++MainPackageView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainpackageview.xaml
++MainProductionSewView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainproductionsewview.xaml
++MainUnIssueShopOrderView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainunissueshoporderview.xaml
++INC.Production.csproj.vspscc
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\inc.production.csproj.vspscc
++ProductionExecutionBusinessModule.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\productionexecutionbusinessmodule.cs
++disconnect.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\disconnect.png
++edit-square.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\edit-square.png
++file-delete.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\file-delete.png
++person.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\person.png
++printer.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\printer.png
++search.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\search.png
++旗标_白.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\旗标_白.png
++下.png
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\resources\下.png
++BatchIssueShopOrderViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\batchissueshoporderviewmodel.cs
++ChangeRouteViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\changerouteviewmodel.cs
++CorrectViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\correctviewmodel.cs
++DivideViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\divideviewmodel.cs
++InStorageViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\instorageviewmodel.cs
++InventoryLessViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\inventorylessviewmodel.cs
++IssueShopOrderViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\issueshoporderviewmodel.cs
++PackageViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\packageviewmodel.cs
++ReallocateViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\reallocateviewmodel.cs
++ScrapViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\scrapviewmodel.cs
++SewAssignmentViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\sewassignmentviewmodel.cs
++ShopOrderDivideViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\dialog\shoporderdivideviewmodel.cs
++MainProductionExecutionFootViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\headerfoot\mainproductionexecutionfootviewmodel.cs
++MainProductionExecutionHeaderViewModel.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\viewmodels\headerfoot\mainproductionexecutionheaderviewmodel.cs
++BoolToColorBrushConverter.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\converters\booltocolorbrushconverter.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\converters\booltocolorbrushconverter.cs
++BatchIssueShopOrderView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\batchissueshoporderview.xaml.cs
++ChangeRouteView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\changerouteview.xaml.cs
++CorrectView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\correctview.xaml.cs
++DivideView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\divideview.xaml.cs
++InStorageView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\instorageview.xaml.cs
++InventoryLessView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\inventorylessview.xaml.cs
++IssueShopOrderView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\issueshoporderview.xaml.cs
++ReallocateView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\reallocateview.xaml.cs
++ScrapView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\scrapview.xaml.cs
++SewAssignmentView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\sewassignmentview.xaml.cs
++ShopOrderDivideView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\dialog\shoporderdivideview.xaml.cs
++MainProductionExecutionFootView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\headerfoot\mainproductionexecutionfootview.xaml
++MainProductionExecutionHeaderView.xaml
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\headerfoot\mainproductionexecutionheaderview.xaml
++MainIssueShopOrderView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainissueshoporderview.xaml.cs
++MainPackageView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainpackageview.xaml.cs
++MainProductionSewView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainproductionsewview.xaml.cs
++MainUnIssueShopOrderView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\mainunissueshoporderview.xaml.cs
++Syncfusion.Tools.WPF (28.2.3)
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:>4007
++ProductionExecutionRegionNames.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\models\constants\productionexecutionregionnames.cs
++MainProductionExecutionFootView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\headerfoot\mainproductionexecutionfootview.xaml.cs
++MainProductionExecutionHeaderView.xaml.cs
i:{3b01e865-1808-4ade-8c6b-609e5d6d2cbe}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.productionexecution\views\headerfoot\mainproductionexecutionheaderview.xaml.cs
++EventMessageBase.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\eventmessagebase.cs
++IBusinessModule.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\ibusinessmodule.cs
++INC.BusinessModuleCore.csproj.vspscc
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\inc.businessmodulecore.csproj.vspscc
++Request
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\
++AccessoryModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\accessorymodel.cs
++FabricWidthModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\fabricwidthmodel.cs
++GreigeFabricModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\greigefabricmodel.cs
++PackageModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\packagemodel.cs
++PrintPackageModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\printpackagemodel.cs
++RouteModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\routemodel.cs
++SalesOrderModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\salesordermodel.cs
++ScrapModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\scrapmodel.cs
++ScrapReasonModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\scrapreasonmodel.cs
++ScrapShopOrderModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\scrapshopordermodel.cs
++SewerModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\sewermodel.cs
++ShopOrderModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\shopordermodel.cs
++TransferItemModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\transferitemmodel.cs
++WorkPlaceModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\workplacemodel.cs
++WorkShopModel.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\workshopmodel.cs
++AllocateSewingShopOrderRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\allocatesewingshoporderrequest.cs
++ApplyRouteToShopOrderRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\applyroutetoshoporderrequest.cs
++ChangePackageLocationRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\changepackagelocationrequest.cs
++PackageScrapRequset.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\packagescraprequset.cs
++ReAllocateSewingShopOrderRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\reallocatesewingshoporderrequest.cs
++ReJudgeSewingScrapRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\rejudgesewingscraprequest.cs
++SalesRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\salesrequest.cs
++ScrapRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\scraprequest.cs
++SewScrapRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\sewscraprequest.cs
++ShopOrderRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\shoporderrequest.cs
++SplitShopOrderRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\splitshoporderrequest.cs
++TransferToShopOrdeRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\transfertoshoporderequest.cs
++UnbindRequest.cs
i:{2e601fb6-0800-4b37-a0f1-dd8fcfa4f590}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.businessmodulecore\models\request\unbindrequest.cs
++Behaviors
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\behaviors\
++INC.LoginBusinessModule.csproj.vspscc
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\inc.loginbusinessmodule.csproj.vspscc
++LoginBusinessModule.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\loginbusinessmodule.cs
++PasswordBehavior.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\behaviors\passwordbehavior.cs
++CommonServiceActionRequest.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\common\commonserviceactionrequest.cs
++IDateChangeManger.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\interface\idatechangemanger.cs
++IServiceManager.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\interface\iservicemanager.cs
++IUserManager.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\interface\iusermanager.cs
++IUserSettingsManager.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\interface\iusersettingsmanager.cs
++LoginBusinessModuleChinese.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\language\loginbusinessmodulechinese.xaml
++LoginBusinessModuleEnglish.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\language\loginbusinessmoduleenglish.xaml
++DateChangeManger.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\manager\datechangemanger.cs
++ServiceManager.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\manager\servicemanager.cs
++UserManager.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\manager\usermanager.cs
++UserSettingsManager.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\manager\usersettingsmanager.cs
++Options
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\model\options\
++ApplicationConfigurationModel.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\model\applicationconfigurationmodel.cs
++RoleModel.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\model\rolemodel.cs
++TokenResponseModel.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\model\tokenresponsemodel.cs
++UserSettingsModel.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\model\usersettingsmodel.cs
++LoginButtonDictionary.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\loginbuttondictionary.xaml
++LoginCheckBoxDictionary.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\logincheckboxdictionary.xaml
++LoginDockPanelDictionary.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\logindockpaneldictionary.xaml
++LoginImageDictionary.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\loginimagedictionary.xaml
++LoginTextBlockDictionary.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\logintextblockdictionary.xaml
++LoginTextBoxDictionary.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\logintextboxdictionary.xaml
++LoginUserControlDictionary.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\loginusercontroldictionary.xaml
++LoginViewModel.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\viewmodels\loginviewmodel.cs
++LoginView.xaml
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\views\loginview.xaml
++LoginOptions.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\model\options\loginoptions.cs
++back.png
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\images\back.png
++Close.png
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\resources\images\close.png
++LoginView.xaml.cs
i:{a4f471b2-2e8b-4527-a543-0ab78302ffd6}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.loginbusinessmodule\views\loginview.xaml.cs
++INC.HalfScrap.csproj.vspscc
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\inc.halfscrap.csproj.vspscc
++ParticleMixingBusinessModule.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\particlemixingbusinessmodule.cs
++MainPagckageScrapViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\mainpagckagescrapviewmodel.cs
++MainSewScrapViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\mainsewscrapviewmodel.cs
++MainPagckageScrapView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\mainpagckagescrapview.xaml
++MainSewScrapView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\mainsewscrapview.xaml
++ParticleMixingRegionNames.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\models\constants\particlemixingregionnames.cs
++PackageReJudgeViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\dialog\packagerejudgeviewmodel.cs
++ReJudgeViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\dialog\rejudgeviewmodel.cs
++SewReJudgeViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\dialog\sewrejudgeviewmodel.cs
++MainParticleHeatingFootViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\headerfoot\mainparticleheatingfootviewmodel.cs
++MainParticleHeatingHeaderViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\headerfoot\mainparticleheatingheaderviewmodel.cs
++MainParticleMixingFootViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\headerfoot\mainparticlemixingfootviewmodel.cs
++MainParticleMixingHeaderViewModel.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\viewmodels\headerfoot\mainparticlemixingheaderviewmodel.cs
++PackageReJudgeView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\dialog\packagerejudgeview.xaml
++ReJudgeView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\dialog\rejudgeview.xaml
++SewReJudgeView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\dialog\sewrejudgeview.xaml
++MainParticleHeatingFootView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticleheatingfootview.xaml
++MainParticleHeatingHeaderView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticleheatingheaderview.xaml
++MainParticleMixingFootView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticlemixingfootview.xaml
++MainParticleMixingHeaderView.xaml
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticlemixingheaderview.xaml
++MainPagckageScrapView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\mainpagckagescrapview.xaml.cs
++MainSewScrapView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\mainsewscrapview.xaml.cs
++PackageReJudgeView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\dialog\packagerejudgeview.xaml.cs
++ReJudgeView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\dialog\rejudgeview.xaml.cs
++SewReJudgeView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\dialog\sewrejudgeview.xaml.cs
++MainParticleHeatingFootView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticleheatingfootview.xaml.cs
++MainParticleHeatingHeaderView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticleheatingheaderview.xaml.cs
++MainParticleMixingFootView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticlemixingfootview.xaml.cs
++MainParticleMixingHeaderView.xaml.cs
i:{8f3f60b8-f0bc-4fdf-9eb4-697750b8bf02}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\04_businessmodules\inc.particlemixing\views\headerfoot\mainparticlemixingheaderview.xaml.cs
++05_FunctionModules
i:{00000000-0000-0000-0000-000000000000}:05_FunctionModules
++Plc
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\plc\
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\plc\
++SerialPort
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\serialport\
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\serialport\
++INC.DeviceFunctionModule.csproj.vspscc
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\inc.devicefunctionmodule.csproj.vspscc
++Implements
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\plc\implements\
++DevicePlcFunctionModule.cs
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\plc\deviceplcfunctionmodule.cs
++DeviceSerialPortFunctionModule.cs
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\serialport\deviceserialportfunctionmodule.cs
++ScanManager.cs
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\serialport\scanmanager.cs
++HslCommunication (12.1.3)
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3857
++System.IO.Ports (9.0.0)
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:>3855
++MelsecPlcReadWriteManager.cs
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\plc\implements\melsecplcreadwritemanager.cs
++SiemensPlcReadWriteManager.cs
i:{8dab3377-227f-49b3-b0c1-53bca33c9370}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.devicefunctionmodule\plc\implements\siemensplcreadwritemanager.cs
++Database
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\
++Excel
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\excel\
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\
++Log
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\log\
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\log\
++INC.CompactFunctionModule.csproj.vspscc
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\inc.compactfunctionmodule.csproj.vspscc
++Sqlite
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\sqlite\
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlite\
++SqlServer
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\sqlserver\
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlserver\
++CompactExcelConfigFunctionModule.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\excel\compactexcelconfigfunctionmodule.cs
++CompactExcelConfigManagerFactory.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\excel\compactexcelconfigmanagerfactory.cs
++ExcelConfigManager.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\excel\excelconfigmanager.cs
++CompactLogFunctionModule.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\log\compactlogfunctionmodule.cs
++LoggerExtensions.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\log\loggerextensions.cs
++Dapper (2.1.35)
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3802
++Dapper.Contrib (2.0.78)
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3807
++System.Data.SqlClient (4.9.0)
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3812
++System.Data.SQLite.Core (1.0.119)
i:{2460b747-e1df-443b-94c6-1378b86df32f}:>3803
++CompactLocalCacheFunctionModule.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\sqlite\compactlocalcachefunctionmodule.cs
++LocalCacheManager.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\sqlite\localcachemanager.cs
++CompactSqlServerDatabaseOperatorFactory.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\sqlserver\compactsqlserverdatabaseoperatorfactory.cs
++CompactSqlServerFunctionModule.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\sqlserver\compactsqlserverfunctionmodule.cs
++SqlServerDatabaseOperator.cs
i:{2460b747-e1df-443b-94c6-1378b86df32f}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.compactfunctionmodule\database\sqlserver\sqlserverdatabaseoperator.cs
++AutoUpdateFunctionModule.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\autoupdatefunctionmodule.cs
++INC.AutoUpdateFunctionModule.csproj.vspscc
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\inc.autoupdatefunctionmodule.csproj.vspscc
++IAutoUpdateManager.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\interface\iautoupdatemanager.cs
++AutoUpdateManager.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\manager\autoupdatemanager.cs
++DtoCheckUpdateOutput.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\dtocheckupdateoutput.cs
++DtoConfirmUpdatedOutput.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\dtoconfirmupdatedoutput.cs
++DtoDownloadConfigsOutput.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\dtodownloadconfigsoutput.cs
++DtoDownloadDllsOutput.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\dtodownloaddllsoutput.cs
++DtoUpdateInput.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\dtoupdateinput.cs
++FileContent.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\filecontent.cs
++RequestModel.cs
i:{103f8f4e-1555-4081-8818-c00c0c72e165}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.autoupdatefunctionmodule\model\requestmodel.cs
++AutoUpdate
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\autoupdate\
++Compact
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\
++Device
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\
++ContainerRegistryExtension.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\containerregistryextension.cs
++IFunctionModule.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\ifunctionmodule.cs
++INC.FunctionModuleCore.csproj.vspscc
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\inc.functionmodulecore.csproj.vspscc
++AutoUpdateOptions.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\autoupdate\autoupdateoptions.cs
++Syncfusion.XlsIO.Net.Core (27.2.4)
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:>3984
++ColumnAttribute.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\columnattribute.cs
++CompactExcelConfigOptions.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\compactexcelconfigoptions.cs
++ExcelDataInfo.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\exceldatainfo.cs
++ICompactExcelConfigManagerFactory.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\icompactexcelconfigmanagerfactory.cs
++IExcelConfigManager.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\iexcelconfigmanager.cs
++SheetAttribute.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\sheetattribute.cs
++TypeConverter.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\excel\typeconverter.cs
++CompactLogOptions.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\log\compactlogoptions.cs
++DevicePlcOptions.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\plc\deviceplcoptions.cs
++IPlcReadWriteManager.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\plc\iplcreadwritemanager.cs
++PlcCategory.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\plc\plccategory.cs
++DeviceSerialPortOption.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\serialport\deviceserialportoption.cs
++IScanManager.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\device\serialport\iscanmanager.cs
++CompactSqliteDbOptions.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlite\compactsqlitedboptions.cs
++ILocalCacheManager.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlite\ilocalcachemanager.cs
++LocalCacheModel.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlite\localcachemodel.cs
++CompactSqlServerDbOptions.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlserver\compactsqlserverdboptions.cs
++ICompactSqlServerDatabaseOperatorFactory.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlserver\icompactsqlserverdatabaseoperatorfactory.cs
++ISqlServerDatabaseOperator.cs
i:{7a9b0557-23ac-424e-8e98-adb9c1864b4a}:c:\users\<USER>\source\workspaces\006_jh_mes\desktop\05_functionmodules\inc.functionmodulecore\compact\database\sqlserver\isqlserverdatabaseoperator.cs
