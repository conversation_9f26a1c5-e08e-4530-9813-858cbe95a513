﻿using CommunityToolkit.Mvvm.ComponentModel;

namespace INC.BusinessModuleCore.Models
{
    public class ShopOrderModel : ObservableObject
    {
        private bool _select;
        public int No { get; set; }
        public int DBPK { get; set; }
        public string ShopOrderNo { get; set; }
        public string CustomerName { get; set; }
        public string ItemName { get; set; }
        public string ItemNo { get; set; }
        public int Quantity { get; set; }
        public string SalesOrderNo { get; set; }
        public DateTime RequireDate { get; set; }
        public DateTime CreationDate { get; set; }
        public string Assignment { get; set; }
        public string Operator { get; set; }
        public string UWB { get; set; }
        public int CompletedQuantity { get; set; }
        public string Color { get; set; }
        public string Size { get; set; }
        public string RouteName { get; set; }
        public bool Status { get; set; }
        public string StautsDescription => Status ? "已完成" : "生产中";
        public string SalesItem { get; set; }
        public string Location { get; set; }
        public string CustomerOrderNo { get; set; }
        public string ItemType { get; set; }
        public string ShopOrderWorkStation { get; set; }
        public string CutDate { get; set; } 
        public bool Select
        {
            get => _select;
            set
            {
                if (value == _select) return;
                _select = value;
                OnPropertyChanged();
            }
        }
    }
}
