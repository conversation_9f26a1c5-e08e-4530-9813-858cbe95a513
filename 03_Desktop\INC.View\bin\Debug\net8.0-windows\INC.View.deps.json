{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"INC.View/1.0.0": {"dependencies": {"INC.HalfScrap": "1.0.0", "INC.Production": "1.0.0", "INC.ViewModel": "1.0.0"}, "runtime": {"INC.View.dll": {}}}, "CommunityToolkit.Mvvm/8.3.2": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.3.0.0", "fileVersion": "8.3.2.1"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.135.29210"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "Prism.Container.Abstractions/9.0.106": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Prism.Container.Abstractions.dll": {"assemblyVersion": "9.0.106.9543", "fileVersion": "9.0.106.9543"}}}, "Prism.Container.Unity/9.0.106": {"dependencies": {"Prism.Container.Abstractions": "9.0.106", "Unity.Container": "5.11.11"}, "runtime": {"lib/net8.0/Prism.Container.Unity.dll": {"assemblyVersion": "9.0.106.9543", "fileVersion": "9.0.106.9543"}}}, "Prism.Core/9.0.537": {"dependencies": {"Prism.Container.Abstractions": "9.0.106", "Prism.Events": "9.0.537"}, "runtime": {"lib/net6.0/Prism.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Events/9.0.537": {"runtime": {"lib/net6.0/Prism.Events.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Unity/9.0.537": {"dependencies": {"Prism.Container.Unity": "9.0.106", "Prism.Wpf": "9.0.537"}, "runtime": {"lib/net6.0-windows7.0/Prism.Unity.Wpf.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Wpf/9.0.537": {"dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.135", "Prism.Core": "9.0.537"}, "runtime": {"lib/net6.0-windows7.0/Prism.Wpf.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Serilog/4.1.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.1.0.0"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.1.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.1.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Syncfusion.Compression.Net.Core/27.2.4": {"runtime": {"lib/net8.0/Syncfusion.Compression.Portable.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Data.WPF/27.2.4": {"runtime": {"lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Licensing/28.2.3": {"runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {"assemblyVersion": "28.2.3.0", "fileVersion": "28.2.3.0"}}}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfGrid.WPF/27.2.4": {"dependencies": {"Syncfusion.Data.WPF": "27.2.4", "Syncfusion.Licensing": "28.2.3", "Syncfusion.Shared.WPF": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.SfTreeView.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGridCommon.WPF": "27.2.4", "Syncfusion.Shared.WPF": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Shared.WPF/28.2.3": {"dependencies": {"Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"assemblyVersion": "28.2.3.0", "fileVersion": "28.2.3.0"}}}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "Syncfusion.Tools.WPF/28.2.3": {"dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.Shared.WPF": "28.2.3"}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll": {"assemblyVersion": "28.2.3.0", "fileVersion": "28.2.3.0"}}}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"dependencies": {"Syncfusion.Compression.Net.Core": "27.2.4", "Syncfusion.Licensing": "28.2.3"}, "runtime": {"lib/net8.0/Syncfusion.XlsIO.Portable.dll": {"assemblyVersion": "27.2.4.0", "fileVersion": "27.2.4.0"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {}, "System.Threading.Tasks.Extensions/4.5.2": {}, "Unity.Abstractions/5.11.7": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netcoreapp3.0/Unity.Abstractions.dll": {"assemblyVersion": "5.11.7.0", "fileVersion": "5.11.7.0"}}}, "Unity.Container/5.11.11": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2", "Unity.Abstractions": "5.11.7"}, "runtime": {"lib/netcoreapp3.0/Unity.Container.dll": {"assemblyVersion": "5.11.11.0", "fileVersion": "5.11.11.0"}}}, "INC.BusinessModuleCore/1.0.0": {"dependencies": {"INC.FunctionModuleCore": "1.0.0", "INC.ViewCore": "1.0.0", "INC.ViewModelCore": "1.0.0"}, "runtime": {"INC.BusinessModuleCore.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.Common/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.1", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"INC.Common.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.FunctionModuleCore/1.0.0": {"dependencies": {"INC.Common": "1.0.0", "Prism.Core": "9.0.537", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "Syncfusion.XlsIO.Net.Core": "27.2.4"}, "runtime": {"INC.FunctionModuleCore.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.HalfScrap/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.LoginBusinessModule": "1.0.0"}, "runtime": {"INC.HalfScrap.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.LoginBusinessModule/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0"}, "runtime": {"INC.LoginBusinessModule.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.Production/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.LoginBusinessModule": "1.0.0", "Syncfusion.Tools.WPF": "28.2.3"}, "runtime": {"INC.Production.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.ViewCore/1.0.0": {"dependencies": {"INC.ViewModelCore": "1.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.135", "Prism.Core": "9.0.537", "Prism.Unity": "9.0.537", "Syncfusion.Licensing": "28.2.3", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGrid.WPF": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4", "Syncfusion.SfTreeView.WPF": "27.2.4", "Syncfusion.Themes.MaterialDark.WPF": "27.2.4", "Syncfusion.Themes.MaterialDarkBlue.WPF": "27.2.4", "Syncfusion.Themes.MaterialLight.WPF": "27.2.4", "Syncfusion.Themes.MaterialLightBlue.WPF": "27.2.4", "Syncfusion.Tools.WPF": "28.2.3"}, "runtime": {"INC.ViewCore.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.ViewModel/1.0.0": {"dependencies": {"INC.BusinessModuleCore": "1.0.0", "INC.HalfScrap": "1.0.0", "INC.LoginBusinessModule": "1.0.0", "INC.Production": "1.0.0"}, "runtime": {"INC.ViewModel.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "INC.ViewModelCore/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.3.2", "INC.Common": "1.0.0", "Prism.Core": "9.0.537"}, "runtime": {"INC.ViewModelCore.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "DevExpress.XtraReports.v24.1/24.1.6.0": {"runtime": {"DevExpress.XtraReports.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.DataAccess.v24.1/24.1.6.0": {"runtime": {"DevExpress.DataAccess.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Printing.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Printing.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Data.v24.1/24.1.6.0": {"runtime": {"DevExpress.Data.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.XtraCharts.v24.1/24.1.6.0": {"runtime": {"DevExpress.XtraCharts.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Xpo.v24.1/24.1.6.0": {"runtime": {"DevExpress.Xpo.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Drawing.v24.1/24.1.6.0": {"runtime": {"DevExpress.Drawing.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Sparkline.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Sparkline.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.PivotGrid.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.PivotGrid.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Charts.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Charts.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.XtraGauges.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.XtraGauges.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Pdf.v24.1.Drawing/24.1.6.0": {"runtime": {"DevExpress.Pdf.v24.1.Drawing.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Pdf.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Pdf.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.RichEdit.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.RichEdit.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.Office.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.Office.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.CodeParser.v24.1/24.1.6.0": {"runtime": {"DevExpress.CodeParser.v24.1.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}, "DevExpress.DataVisualization.v24.1.Core/24.1.6.0": {"runtime": {"DevExpress.DataVisualization.v24.1.Core.dll": {"assemblyVersion": "24.1.6.0", "fileVersion": "24.1.6.0"}}}}}, "libraries": {"INC.View/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-m8EolE1A0Updj68WTsZSGI6VWb6mUqHPh7QFo0kt7+JPhYMNXRS1ch8TS/oITAdcxTLrwMOp3ku1KjeG1/Zdpg==", "path": "communitytoolkit.mvvm/8.3.2", "hashPath": "communitytoolkit.mvvm.8.3.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"type": "package", "serviceable": true, "sha512": "sha512-r8qBEXmQfORso2+MVHnt8PSH4761zJ0SIxgQTSEDVLU97EN2FZdG6/ZCYUPhQy+OrPKgnpYBCAs3PS6Bs7wRsg==", "path": "microsoft.xaml.behaviors.wpf/1.1.135", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Prism.Container.Abstractions/9.0.106": {"type": "package", "serviceable": true, "sha512": "sha512-QNOERNOqsxvAa8pbWjqFB872DkvYK/cVRrcFO5vJYgWTIKBd8xfaI/jaZ0qeXLYVDz0nrvgJTZVVnip6+68dCw==", "path": "prism.container.abstractions/9.0.106", "hashPath": "prism.container.abstractions.9.0.106.nupkg.sha512"}, "Prism.Container.Unity/9.0.106": {"type": "package", "serviceable": true, "sha512": "sha512-QRakEz+1HG7PGETsEWQnHED4tmp7Ir/lVIVo0TySER1ACqNGNQgAfSgza+B/WMl/SadHhrz+HlTVQw3+PrAFWQ==", "path": "prism.container.unity/9.0.106", "hashPath": "prism.container.unity.9.0.106.nupkg.sha512"}, "Prism.Core/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-D7mEqPKLVNrD0g2WHCpC/MOKwn8h7X1liCWyjqjL7NCuxgwuhVLTG85E/ZPBkISrXdwvOQZ+bSY31bvP79FQlg==", "path": "prism.core/9.0.537", "hashPath": "prism.core.9.0.537.nupkg.sha512"}, "Prism.Events/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-Pzp5MGUuhAyKXZUbHVYNWLGF/eA3sScqDN6VrzbWlKj85R0IS0q+JXe99umynso2xhXAe+1jrQCCkgqmEFCBng==", "path": "prism.events/9.0.537", "hashPath": "prism.events.9.0.537.nupkg.sha512"}, "Prism.Unity/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-F2RjW2QZg2TsQxuYsRB0ldoacQw2xuZmaMM1LENfR+qbxPxBXC887yZ+PKeP9eWPP2sP3oVUqo09N8EWJLZXng==", "path": "prism.unity/9.0.537", "hashPath": "prism.unity.9.0.537.nupkg.sha512"}, "Prism.Wpf/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-srsXhi7FRUFawsNoRkY67duMEGjZo3ff0FpqpkjeWkkAuLazlH1UmNVrvwnpaLQCBboexH/z6oGrLvpeocxgdw==", "path": "prism.wpf/9.0.537", "hashPath": "prism.wpf.9.0.537.nupkg.sha512"}, "Serilog/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-u1aZI8HZ62LWlq5dZLFwm6jMax/sUwnWZSw5lkPsCt518cJBxFKoNmc7oSxe5aA5BgSkzy9rzwFGR/i/acnSPw==", "path": "serilog/4.1.0", "hashPath": "serilog.4.1.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Syncfusion.Compression.Net.Core/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-1+bVigMEnIxAbqVoIpIwOmX/LzJpHFCcSLJLsPo1x3Jk8khAXXPbZg/reH8mIPDEYVVNIifmx3RE6VJZz3/Uzw==", "path": "syncfusion.compression.net.core/27.2.4", "hashPath": "syncfusion.compression.net.core.27.2.4.nupkg.sha512"}, "Syncfusion.Data.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-7KPOZZXA8aNGOX1V0VY6hV3X8xX2Fyu+Dcbe7VEIcZhSTS2E/CZ7PNNmczParX+VmDyz1tD7k8mFHEN5NPf4kg==", "path": "syncfusion.data.wpf/27.2.4", "hashPath": "syncfusion.data.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Licensing/28.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-EjsEHdkZwvViixabnrb2ZGmvg/a79Y01gLuOQzoMrRNUaOkQ/c71B+7eHoLUEJEt7H7OWNhlcKcbGtTmA75MBA==", "path": "syncfusion.licensing/28.2.3", "hashPath": "syncfusion.licensing.28.2.3.nupkg.sha512"}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-z06nYGZkmeChFD1/ct8dD7N642LXUM26H2lJoRq/p5kJlkETi3ax1p0DzkdksXMNvSN/V1yr+Vf2NLPEGTX9IQ==", "path": "syncfusion.sfbusyindicator.wpf/27.2.4", "hashPath": "syncfusion.sfbusyindicator.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfGrid.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-852wa/nHP9/lCxg0p1Dyy+oqepcFwXNqpJzrBEyKTbKpHAN2ly8z2QjViNXfvOw7zjXh4OftGL2RUlWTrK6CSQ==", "path": "syncfusion.sfgrid.wpf/27.2.4", "hashPath": "syncfusion.sfgrid.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-I/IXKLY3rmzgbIsETpf7bz2HEVnEjDS7MUaQ9IgzCENzwrA8K42FMAFO3KZeWJmTGQZnR3YG+ukPfZsz18LGWA==", "path": "syncfusion.sfgridcommon.wpf/27.2.4", "hashPath": "syncfusion.sfgridcommon.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-omu/q1JaEydjM8F+WWIY7SGCa8/KMa87RfGaTIVMnWHru0b/Zcv6u/VsiSPuVTijMpp1shu+Kh5Phm0GnDRufA==", "path": "syncfusion.sfskinmanager.wpf/27.2.4", "hashPath": "syncfusion.sfskinmanager.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.SfTreeView.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-LC/DZc7UG/5eBlL3HDjMGO5JzDRtjguH+yVttooY5odg6e9hP5X0znsSF83EcnOkwH7s0JR6OLnchBUQz9eu7w==", "path": "syncfusion.sftreeview.wpf/27.2.4", "hashPath": "syncfusion.sftreeview.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Shared.WPF/28.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-qHQ2RH6d7vUf3NCAzHpOykzIYcZO6TfSA4DIrBis9TN3sl0g7u4WV3YMtt9szcRAGWBXqosCozwMbIbBh0IpgQ==", "path": "syncfusion.shared.wpf/28.2.3", "hashPath": "syncfusion.shared.wpf.28.2.3.nupkg.sha512"}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-HFtbwZB+ZwzWP2K6UhJZDA4ORHHYs5evUwPwAC/UnL//05mWnYYKQY8+IXn2Hx4Wg1pIRUn/fc+sYvGvSS3AvQ==", "path": "syncfusion.themes.materialdark.wpf/27.2.4", "hashPath": "syncfusion.themes.materialdark.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-9ickWilfXrisH+bk1w4fZxbASCZQoANjbKulQ1KG8G1WdWj3M+wbrBIQ4dK577kbNpd9NtXJJK5NXuIJFWBkEQ==", "path": "syncfusion.themes.materialdarkblue.wpf/27.2.4", "hashPath": "syncfusion.themes.materialdarkblue.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-OO6N/u+pzNVkY50uGSCz14ZSTA2PY38Qw4L9dehLm3pVOe1+4PBDQDxlaV2TWyfw+yaUF+sX2WTyxPfl23QGGw==", "path": "syncfusion.themes.materiallight.wpf/27.2.4", "hashPath": "syncfusion.themes.materiallight.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-wOdsaGE/gUtrJHiSJ4j0D2UkXAb0gvJQ0+91SOypmH5N8Z1ytIwW6XujZKBxAp95lm+PZV0sXLekmJ4nGaU6nQ==", "path": "syncfusion.themes.materiallightblue.wpf/27.2.4", "hashPath": "syncfusion.themes.materiallightblue.wpf.27.2.4.nupkg.sha512"}, "Syncfusion.Tools.WPF/28.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-vfAblki/RZQa8TBESUb0yF8iKwmURXJ4nl5rLRnp2W7FkHirLEitK6hTN3Jr7C9qjaDsECzFPK/3g4thHJhXiw==", "path": "syncfusion.tools.wpf/28.2.3", "hashPath": "syncfusion.tools.wpf.28.2.3.nupkg.sha512"}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-IlY9QaOZhjl+rJ0Q+I8PZZ7s5d26/Uw3hzQ9t08hEXfuq/kEkty3zbTmkYQZTOq6L4kaXjwApAGnQwxDCsPsow==", "path": "syncfusion.xlsio.net.core/27.2.4", "hashPath": "syncfusion.xlsio.net.core.27.2.4.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "path": "system.threading.tasks.extensions/4.5.2", "hashPath": "system.threading.tasks.extensions.4.5.2.nupkg.sha512"}, "Unity.Abstractions/5.11.7": {"type": "package", "serviceable": true, "sha512": "sha512-3ztwGEpe35UJlCUswXoi4uVDp8bJsgPsOmO71nZnNXh51II7t54AbezDbS6sR2z4QnMOpNGDaXbsEkyg6dIfOQ==", "path": "unity.abstractions/5.11.7", "hashPath": "unity.abstractions.5.11.7.nupkg.sha512"}, "Unity.Container/5.11.11": {"type": "package", "serviceable": true, "sha512": "sha512-47u4MBG8hxV2ZBUK7LlXcZQW8yWSqUSCRG+2/TBA2CSkxkQlMfVUJ0RJODJsZgsiSgy4N0M8HIr7J88drYR/OQ==", "path": "unity.container/5.11.11", "hashPath": "unity.container.5.11.11.nupkg.sha512"}, "INC.BusinessModuleCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.FunctionModuleCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.HalfScrap/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.LoginBusinessModule/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.Production/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.ViewCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.ViewModel/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "INC.ViewModelCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "DevExpress.XtraReports.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.DataAccess.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Printing.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Data.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.XtraCharts.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Xpo.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Drawing.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Sparkline.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.PivotGrid.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Charts.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.XtraGauges.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Pdf.v24.1.Drawing/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Pdf.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.RichEdit.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.Office.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.CodeParser.v24.1/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}, "DevExpress.DataVisualization.v24.1.Core/24.1.6.0": {"type": "reference", "serviceable": false, "sha512": ""}}}