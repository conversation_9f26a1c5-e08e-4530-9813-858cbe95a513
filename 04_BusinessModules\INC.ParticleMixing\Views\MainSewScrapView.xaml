<UserControl x:Class="INC.HalfScrap.Views.MainSewScrapView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.HalfScrap.Views"
             xmlns:viewModels="clr-namespace:INC.HalfScrap.ViewModels"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             xmlns:converters="clr-namespace:INC.HalfScrap.Views.Converters"
             d:DataContext="{d:DesignInstance viewModels:MainSewScrapViewModel}"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <converters:BoolToColorBrushConverter x:Key="BoolToColorBrushConverter"/>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="10"/>
            <ColumnDefinition Width="2*"/>
        </Grid.ColumnDefinitions>
        <Grid Grid.Column="1" Background="#F6F6F6"></Grid>
        <Grid Grid.Column="0" Background="White">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="140" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="140" />
                </Grid.RowDefinitions>
                <TextBlock
                    Grid.Row="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Text="工单数量调整"
                        FontSize="32"/>
                <Grid Grid.Row="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="2*"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="2*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="工单号码:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="2" Text="{Binding ShopOrderNo,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center"
                             Width="300" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" Height="45" Padding="5,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding QueryShopOrderCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="当前制程:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <ComboBox Grid.Row="1" Grid.Column="2"  FontSize="24" HorizontalAlignment="Left" 
                              ItemsSource="{Binding ScrapShopOrder.Routes}" SelectedItem="{Binding SelectRoute,UpdateSourceTrigger=PropertyChanged}" Padding="5,0"
                              VerticalAlignment="Center" VerticalContentAlignment="Center" Width="300" Height="45" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" DisplayMemberPath="RouteName"/>
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="产品号码:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="2" Text="{Binding ScrapShopOrder.ItemNo}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center"
                             Width="300" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" Height="45" IsEnabled="False" Padding="5,0"/>
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="产品名称:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="3" Grid.Column="2" Text="{Binding ScrapShopOrder.ItemName}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center"
                             Width="300" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" Height="45" IsEnabled="False" Padding="5,0"/>
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="工单数量:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="4" Grid.Column="2" Text="{Binding ScrapShopOrder.Quantity}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center"
                             Width="300" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" Height="45" IsEnabled="False" Padding="5,0"/>
                    <TextBlock Grid.Row="5" Grid.Column="0" Text="良品数量:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="5" Grid.Column="2" Text="{Binding ScrapShopOrder.GoodQuantity}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center"
                             Width="300" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" Height="45" IsEnabled="False" Padding="5,0"/>
                    <TextBlock Grid.Row="6" Grid.Column="0" Text="产品颜色:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center" />
                    <TextBox Grid.Row="6" Grid.Column="2" Text="{Binding ScrapShopOrder.Color}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="5,0"
                             Height="45" Width="300" BorderThickness="1" BorderBrush="#4D333333" IsEnabled="False"/>
                    <TextBlock Grid.Row="7" Grid.Column="0" Text="产品尺寸:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center" />
                    <TextBox Grid.Row="7" Grid.Column="2" Text="{Binding ScrapShopOrder.ItemType}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="5,0"
                             Height="45" Width="300" BorderThickness="1" BorderBrush="#4D333333" IsEnabled="False"/>
                    <TextBlock Grid.Row="8" Grid.Column="0" Text="调整原因:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <StackPanel Grid.Row="8" Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Left">
                        <TextBlock Text="报废:" FontSize="22" VerticalAlignment="Center"/>
                        <CheckBox IsChecked="{Binding IsScrap,UpdateSourceTrigger=PropertyChanged}"
                                  HorizontalAlignment="Center" VerticalAlignment="Center" BorderBrush="Black" Margin="10,0">
                            <CheckBox.LayoutTransform>
                                <ScaleTransform ScaleX="1.5" ScaleY="1.5" />
                            </CheckBox.LayoutTransform>
                        </CheckBox>
                        <TextBlock Text="数量调整:" FontSize="22" VerticalAlignment="Center"/>
                        <CheckBox IsChecked="{Binding IsAdd,UpdateSourceTrigger=PropertyChanged}"
                                  HorizontalAlignment="Center" VerticalAlignment="Center" BorderBrush="Black" Margin="10,0">
                            <CheckBox.LayoutTransform>
                                <ScaleTransform ScaleX="1.5" ScaleY="1.5" />
                            </CheckBox.LayoutTransform>
                        </CheckBox>
                    </StackPanel>
                    <TextBlock Grid.Row="9" Grid.Column="0" Text="调整数量:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="9" Grid.Column="2" Text="{Binding Quantity,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center"
                             Width="300" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" Height="45" Padding="5,0"/>
                    <TextBlock Grid.Row="10" Grid.Column="0" Text="调整原因:" FontSize="24" HorizontalAlignment="Right" VerticalAlignment="Center" />
                    <TextBox Grid.Row="10" Grid.Column="2" Text="{Binding Reason, Mode=TwoWay ,UpdateSourceTrigger=PropertyChanged}" FontSize="24" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="5,0"
                             Height="45" Width="300" BorderThickness="1" BorderBrush="#4D333333"/>
                    <!--<ComboBox Grid.Row="5" Grid.Column="2"  FontSize="24" HorizontalAlignment="Left" 
                          ItemsSource="{Binding Reasons}" SelectedItem="{Binding SelectReason,UpdateSourceTrigger=PropertyChanged}" Padding="5,0"
                          VerticalAlignment="Center" VerticalContentAlignment="Center" Width="300" Height="45" BorderThickness="1" BorderBrush="#4D333333" Background="#FAFAFA" DisplayMemberPath="Description"/>-->
                    <Button Grid.Row="11" Grid.Column="0" Grid.ColumnSpan="3" Margin="5,0" Height="70" Command="{Binding ScrapCommand}" Width="420">
                        <Button.Template>
                            <ControlTemplate>
                                <Border BorderBrush="Transparent " CornerRadius="4"
                                        BorderThickness="0" Background="#00078A">
                                    <Label
                                            VerticalContentAlignment="Center"
                                            HorizontalAlignment="Center"
                                            Content="确认"
                                            FontSize="26"
                                            Foreground="White" />
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </Grid>
            </Grid>
        </Grid>
        <Grid Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="65"/>
                <RowDefinition Height="65"/>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="10"/>
            </Grid.RowDefinitions>
            <Grid Grid.Row="0" Background="White">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="5"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="3*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="工单号:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                    <TextBox  MinWidth="180" Text="{Binding QueryShopOrderNo,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}"
              VerticalAlignment="Center" VerticalContentAlignment="Center" BorderBrush="#4D333333" BorderThickness="1" FontSize="18" Margin="5,0" Padding="5,0" Height="35"/>
                </StackPanel>
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="产品名称:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                    <TextBox  MinWidth="180" Text="{Binding QueryItemName,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}"
              VerticalAlignment="Center" VerticalContentAlignment="Center" BorderBrush="#4D333333" BorderThickness="1" FontSize="18" Margin="5,0" Padding="5,0" Height="35"/>
                </StackPanel>
                <StackPanel Grid.Column="3" Orientation="Horizontal">
                    <TextBlock Text="时间:" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                    <syncfusion:DateTimeEdit Grid.Row="3" Grid.Column="2" Width="150" Text="{Binding  StartDate, UpdateSourceTrigger=PropertyChanged}" Height="35" 
                                             HorizontalAlignment="Left" FontSize="18" Margin="5,0" BorderThickness="1" BorderBrush="#4D333333" x:Name="DateTimeEdit1"/>
                    <TextBlock Text="—" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" Margin="5,0"/>
                    <syncfusion:DateTimeEdit Grid.Row="3" Grid.Column="2" Width="150" Text="{Binding EndDate, UpdateSourceTrigger=PropertyChanged}" 
                                             Height="35" HorizontalAlignment="Left" FontSize="18" Margin="5,0" BorderThickness="1" BorderBrush="#4D333333" x:Name="DateTimeEdit2"/>
                </StackPanel>
                <Button Grid.Column="4" Margin="5,0" HorizontalAlignment="Left" Height="40" Command="{Binding QueryCommand}">
                    <Button.Template>
                        <ControlTemplate>
                            <Border BorderBrush="#0003B0 " CornerRadius="2"
                    BorderThickness="1">
                                <StackPanel Orientation="Horizontal" Margin="35,0">
                                    <Label
                        VerticalContentAlignment="Center"
                        Content="查询"
                        FontSize="18"
                        Foreground="#0003B0" />
                                </StackPanel>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </Grid>
            <Grid Grid.Row="1" Background="#fafafa">
                <TextBlock Text="报废明细" HorizontalAlignment="Left" VerticalAlignment="Center" FontSize="20" Margin="5,0"/>
                <Button Margin="5,0" HorizontalAlignment="Right" Height="40" Command="{Binding ReJudgeCommand}">
                    <Button.Template>
                        <ControlTemplate>
                            <Border BorderBrush="Transparent " CornerRadius="4"
                            BorderThickness="0" Background="#FAAD14">
                                <StackPanel Orientation="Horizontal" Margin="35,0">
                                    <Label
                                VerticalContentAlignment="Center"
                                Content="复判"
                                FontSize="18"
                                Foreground="White" />
                                </StackPanel>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </Grid>
            <Grid Grid.Row="2" Background="#F0F0F0" Margin="0,0,13,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Column="0" Text="选择" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                <TextBlock Grid.Column="1" Text="工单号码" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                <TextBlock Grid.Column="2" Text="制程" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="3" Text="产品名称" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="4" Text="规格" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="5" Text="颜色" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="6" Text="数量" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="7" Text="实际报废数" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="8" Text="报废原因" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" />
                <TextBlock Grid.Column="9" Text="操作人" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="10" Text="复判结果" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                <TextBlock Grid.Column="11" Text="操作日期" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
            </Grid>
            <Grid Grid.Row="3">
                <ScrollViewer
             HorizontalScrollBarVisibility="Disabled">
                    <ItemsControl ItemsSource="{Binding ScrapModels , UpdateSourceTrigger=PropertyChanged}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="1" VerticalAlignment="Top" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border  CornerRadius="2" BorderThickness="0,0,0,1" BorderBrush="#F2F3F4 " Height="55" Background="White">
                                    <Border.ToolTip>
                                        <Border
                                            Margin="-6"
                                            Background="#FFEC3D"
                                            CornerRadius="4">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="10" />
                                                    <RowDefinition Height="*" />
                                                    <RowDefinition Height="10" />
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="10" />
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="10" />
                                                </Grid.ColumnDefinitions>
                                                <Grid Grid.Row="1" Grid.Column="1">
                                                    <TextBlock Grid.Row="0" Foreground="Black" FontSize="20">
                                                        <Run Text="产品名称:" />
                                                        <Run Text="{Binding ItemName}" />
                                                    </TextBlock>
                                                </Grid>
                                            </Grid>
                                        </Border>
                                    </Border.ToolTip>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="2*"/>
                                        </Grid.ColumnDefinitions>
                                        <CheckBox Grid.Column="0" IsChecked="{Binding Select,UpdateSourceTrigger=PropertyChanged}" HorizontalAlignment="Center" VerticalAlignment="Center" BorderBrush="Black"
                                                  Command="{Binding RelativeSource={RelativeSource AncestorType=UserControl,Mode=FindAncestor},Path=DataContext.SelectChangeCommand}" CommandParameter="{Binding }">
                                            <CheckBox.LayoutTransform>
                                                <ScaleTransform ScaleX="1.3" ScaleY="1.3" />
                                            </CheckBox.LayoutTransform>
                                        </CheckBox>
                                        <TextBlock Grid.Column="1" Text="{Binding ShopOrderNo}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                        <TextBlock Grid.Column="2" Text="{Binding RouteStepName}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                                        <TextBlock Grid.Column="3" Text="{Binding ItemName}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                                        <TextBlock Grid.Column="4" Text="{Binding ItemType}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                                        <TextBlock Grid.Column="5" Text="{Binding Color}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                                        <TextBlock Grid.Column="6" Text="{Binding Quantity}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                                        <TextBlock Grid.Column="7" Text="{Binding  ActualScrapQuantity}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                                        <TextBlock Grid.Column="8" Text="{Binding ScrapReason}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                        <TextBlock Grid.Column="9" Text="{Binding Operator}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
                                        <TextBlock Grid.Column="10" Text="{Binding confirmDescription}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" 
                                                   Foreground="{Binding Confirm,Converter={StaticResource BoolToColorBrushConverter},ConverterParameter=1}"/>
                                        <TextBlock Grid.Column="11" Text="{Binding CreationDate,StringFormat={}{0:yyyy-MM-dd}}" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" />
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
