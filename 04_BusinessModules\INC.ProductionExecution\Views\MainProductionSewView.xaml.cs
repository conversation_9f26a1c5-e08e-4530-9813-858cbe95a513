﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using INC.BusinessModuleCore.Models;
using INC.Production.ViewModels;
using INC.ProductionExecution.Models;
using INC.ViewModelCore.Message;

namespace INC.Production.Views
{
    /// <summary>
    /// MainProductionExecutionView.xaml 的交互逻辑
    /// </summary>
    public partial class MainProductionSewView : UserControl, IActiveAware
    {
        private readonly IMessageService _messageService;
        private MainProductionSewViewModel _viewModel;
        private bool _isActive;

        public MainProductionSewView(IMessageService messageService, MainProductionSewViewModel viewModel)
        {
            InitializeComponent();
            _messageService = messageService;
            _viewModel = viewModel;
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    IsActiveChanged?.Invoke(this, EventArgs.Empty);

                    // 通知ViewModel页面激活状态变化
                    if (_viewModel != null)
                    {
                        _viewModel.IsActive = value;
                    }
                }
            }
        }

        public event EventHandler IsActiveChanged;

        private void UIElement_OnDragOver(object sender, DragEventArgs e)
        {
            // 检查拖动的数据是否是预期的类型
            if (e.Data.GetDataPresent(typeof(ShopOrderModel)))
            {
                // 设置拖放效果为移动
                e.Effects = DragDropEffects.Move;
            }
            else
            {
                // 如果不支持拖放，设置效果为无
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        private async void UIElement_OnDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(ShopOrderModel)))
            {
                // 获取拖动的数据
                var draggedData = e.Data.GetData(typeof(ShopOrderModel)) as ShopOrderModel;
                // 获取目标 Grid 的 DataContext
                var targetGrid = sender as Border;
                var targetData = targetGrid?.DataContext as WorkPlaceModel;

                var result =
                    await _messageService.Confirm("是否将工单"+ "交给" + targetData.UserName + "?",
                        "提醒");
                if (result)
                {
                    _viewModel.Move(draggedData,targetData);
                }
            }
            e.Handled = true;
        }

        private void UIElement_OnMouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                // 获取拖动的数据
                var data = (ShopOrderModel)((FrameworkElement)sender).DataContext;

                // 启动拖动操作
                DragDrop.DoDragDrop((FrameworkElement)sender, data, DragDropEffects.Move);
            }
        }
    }
}
