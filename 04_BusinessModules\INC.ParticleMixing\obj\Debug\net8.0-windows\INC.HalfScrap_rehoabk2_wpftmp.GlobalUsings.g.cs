// <auto-generated/>
global using global::Prism;
global using global::Prism.Commands;
global using global::Prism.Container.Unity;
global using global::Prism.Dialogs;
global using global::Prism.Events;
global using global::Prism.Ioc;
global using global::Prism.Modularity;
global using global::Prism.Mvvm;
global using global::Prism.Navigation;
global using global::Prism.Navigation.Regions;
global using global::Prism.Unity;
global using global::System;
global using global::System.Collections.Generic;
global using global::System.Linq;
global using global::System.Threading;
global using global::System.Threading.Tasks;
global using global::Unity;
