﻿<UserControl x:Class="INC.View.MainPage.HeaderFunctionView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.View.MainPage"
             xmlns:mainPage="clr-namespace:INC.ViewModel.MainPage;assembly=INC.ViewModel"
             xmlns:converter="clr-namespace:INC.ViewCore.Converter;assembly=INC.ViewCore"
             xmlns:regions="http://prismlibrary.com/"
             xmlns:constants="clr-namespace:INC.ViewModel.Constants;assembly=INC.ViewModel"
             d:DataContext="{d:DesignInstance mainPage:HeaderFunctionViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/ViewToggleButtonDictionary.xaml"/>
                <ResourceDictionary Source="../Resources/ViewButtonDictionary.xaml"/>
                <ResourceDictionary Source="../Resources/ViewBorderDictionary.xaml"/>
                <ResourceDictionary Source="../Resources/FontIcons.xaml"/>
            </ResourceDictionary.MergedDictionaries>


            <converter:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converter:InversionBooleanToVisibilityConverter x:Key="InversionBooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="5"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="5"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!--  宇盛 Logo  -->
        <StackPanel Orientation="Horizontal">
            <Image Source="/INC.View;component/Resources/Images/logo.png"  Margin="5,5"/>
            <TextBlock Text="聚辉纺织" FontSize="20" FontWeight="Bold" VerticalAlignment="Center"></TextBlock>
        </StackPanel>
        

        <!--  Region区域  -->
        <ContentControl 
            Grid.Column="2"
            regions:RegionManager.RegionName="{x:Static constants:RegionNames.MainHeader}" />
        <!--  用户和按钮  -->
        <StackPanel
            Grid.Column="4"
        Margin="6"
        HorizontalAlignment="Right"
        VerticalAlignment="Top"
        Orientation="Horizontal">
            <!--<ToggleButton  x:Name="toggleNotification"
                           IsChecked="{Binding NotificationPanelIsOpen}"
                           Margin="5"
                           Padding="5"
                           Template="{StaticResource UserPanelButton}">
                <StackPanel Orientation="Horizontal">
                    <Image
                    Width="20"
                    Height="20"
                    Source="../Resources/Images/notification.png" />
                </StackPanel>
            </ToggleButton>-->

            <ToggleButton
            IsChecked="{Binding IsShowUserPanel, Mode=TwoWay}"
            Margin="5"
            Padding="10,5"
            Template="{StaticResource UserPanelButton}">
                <StackPanel Orientation="Horizontal">
                    <Image
                    Width="20"
                    Height="20"
                    Source="../Resources/Images/user.png" />
                    <TextBlock Text="{Binding UserName}" Margin="5,0" />
                </StackPanel>
            </ToggleButton>
           

            <Button Command="{Binding MinimizeCommand}"
            Content="{StaticResource Reduce}"
            VerticalAlignment="Center"
            Background="Transparent"
            Style="{DynamicResource IconButtonStyle}" />
            <Button Command="{Binding MaximizeCommand}"
            Content="{StaticResource MaxWindow}"
            Margin="5,0"
            VerticalAlignment="Center"
            Background="Transparent"
            Style="{DynamicResource IconButtonStyle}" />
            <Button Command="{Binding CloseCommand}"
            Content="{StaticResource Close}"
            VerticalAlignment="Center"
            Background="Transparent"
            Style="{DynamicResource IconButtonStyle}" />
        </StackPanel>

        <!--  通知  -->
        <Popup
               Width="400"
               Height="400"
               AllowsTransparency="True"
               HorizontalOffset="0"
               IsOpen="{Binding ElementName=toggleNotification, Path=IsChecked}"
               Placement="Bottom"
               PlacementTarget="{Binding ElementName=toggleNotification}"
               StaysOpen="False"
               VerticalOffset="5">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto" />
                    <RowDefinition />
                    <RowDefinition Height="auto" />
                </Grid.RowDefinitions>

                <Border Grid.Row="0"
                       Grid.RowSpan="3"
                       CornerRadius="5"
                       Opacity="1"
                       Style="{DynamicResource HeaderBorderStyle}">
                    <Border.Effect>
                        <DropShadowEffect
                               BlurRadius="15"
                               Direction="180"
                               Opacity="0.3"
                               ShadowDepth="-10"
                               Color="#1F000000" />
                    </Border.Effect>
                </Border>

                <Border Grid.Row="0" Background="{DynamicResource PrimaryBackground}" CornerRadius="5,5,0,0" />

                <TextBlock Grid.Row="0"
                       Text="通知"
                       Margin="15"
                       FontSize="18"
                       FontWeight="Bold" />

                <Button Grid.Row="0"
                        Command="{Binding NotificationManager.SettingsCommand}"
                        Margin="8,0"
                        Padding="10,5"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"
                        Background="Transparent">
                    <Button.Content>
                        <Image
                               Width="20"
                               Height="20"
                               Source="../Resources/Images/setting.png" />
                    </Button.Content>
                </Button>

                <ItemsControl Grid.Row="1"
                       Margin="10"
                       ItemsSource="{Binding NotificationManager.NotificationMessages}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="auto" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>

                                <Image
                                       Width="35"
                                       Height="35"
                                       Source="../Resources/Images/info.png" />

                                <StackPanel Grid.Column="1" Margin="10,0">
                                    <TextBlock Text="{Binding Content}" TextWrapping="Wrap" />
                                    <TextBlock Text="{Binding CreationTime, StringFormat='yyyy-MM-dd hh:mm:ss'}" Margin="0,2" />
                                </StackPanel>
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <DockPanel Grid.Row="2" Margin="5">
                    <Button
                           Content="设置已读"
                           Command="{Binding NotificationManager.SetAllNotificationsAsReadCommand}"
                           Margin="5"
                           Style="{StaticResource SimpleButton}"
                           Visibility="{Binding NotificationManager.HasUnRead, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <Button
                           Content="查看全部"
                           Command="{Binding NotificationManager.SeeAllNotificationsCommand}"
                           Margin="5"
                           Style="{StaticResource SimpleButton}" />
                </DockPanel>
            </Grid>
        </Popup>
    </Grid>
</UserControl>
