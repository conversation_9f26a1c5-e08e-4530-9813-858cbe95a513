﻿<UserControl x:Class="INC.View.MainPage.StatusBarView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.View.MainPage"
             xmlns:mainPage="clr-namespace:INC.ViewModel.MainPage;assembly=INC.ViewModel"
             xmlns:converters="clr-namespace:INC.View.Converters"
             xmlns:constants="clr-namespace:INC.ViewModel.Constants;assembly=INC.ViewModel"
             xmlns:regions="http://prismlibrary.com/"
             xmlns:converter="clr-namespace:INC.ViewCore.Converter;assembly=INC.ViewCore"
             d:DataContext="{d:DesignInstance mainPage:StatusBarViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <converters:DeviceStatusToColorConverter x:Key="DeviceStatusToColorConverter"/>
            <converter:GridWidthConverter x:Key="GridWidthConverter"/>

        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="5"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="5"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="10"/>
            <ColumnDefinition Width="{Binding RegionWidth, Converter={StaticResource GridWidthConverter}}"/>
            <ColumnDefinition Width="10"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="20"/>
        </Grid.ColumnDefinitions>

        <!--  Region区域  -->
        <ContentControl 
            Grid.Row="1"
            Grid.Column="1"
            regions:RegionManager.RegionName="{x:Static constants:RegionNames.MainFoot}" />

        <Grid Grid.Row="1" Grid.Column="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="{Binding DeviceSpaceLeft, Converter={StaticResource GridWidthConverter}}"/>
                <ColumnDefinition Width="30"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="{Binding DeviceSpaceCenter, Converter={StaticResource GridWidthConverter}}"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Right">
                <TextBlock Text="{DynamicResource Line}" FontSize="15"/>
                <TextBlock Text=": " FontSize="15"/>
                <TextBlock Text="{Binding LineName}" FontSize="15"/>
            </StackPanel>

            <StackPanel Orientation="Horizontal" Grid.Column="2" VerticalAlignment="Center">
                <TextBlock Text="{DynamicResource DeviceStatus}" FontSize="15" VerticalAlignment="Center"/>
                <TextBlock Text=" : " FontSize="15" />
                <TextBlock Text=" " Width="20"/>
                <Canvas>
                    <Path Fill="{Binding DeviceStatus, Converter={StaticResource DeviceStatusToColorConverter}}">
                        <Path.Data>
                            <GeometryGroup>
                                <EllipseGeometry RadiusX="12" RadiusY="12" Center="12,9" />
                            </GeometryGroup>
                        </Path.Data>
                    </Path>
                    <Path Fill="White">
                        <Path.Data>
                            <GeometryGroup>
                                <EllipseGeometry RadiusX="10" RadiusY="10" Center="12,9"/>
                            </GeometryGroup>
                        </Path.Data>
                    </Path>
                    <Path Fill="{Binding DeviceStatus, Converter={StaticResource DeviceStatusToColorConverter}}">
                        <Path.Data>
                            <GeometryGroup>
                                <EllipseGeometry RadiusX="8" RadiusY="8" Center="12,9"/>
                            </GeometryGroup>
                        </Path.Data>
                    </Path>
                </Canvas>
                <TextBlock Text=" " Width="50"/>
                <TextBlock Text="{Binding DeviceStatusDescription}" FontSize="15"/>
                <TextBlock Text="" Width="20"/>
                <!--<TextBlock Text="{Binding DurationSecondFormatString}" Foreground="{Binding DeviceStatus, Converter={StaticResource DeviceStatusToColorConverter}}"
                          FontSize="15"/>-->
            </StackPanel>
            <StackPanel Grid.Column="4" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="{Binding CurrentDateTime}" FontSize="15"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
