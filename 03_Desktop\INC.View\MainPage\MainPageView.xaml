﻿<UserControl x:Class="INC.View.MainPage.MainPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:INC.View.MainPage"
             xmlns:regions="http://prismlibrary.com/"
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             xmlns:mainPage="clr-namespace:INC.ViewModel.MainPage;assembly=INC.ViewModel"
             xmlns:constants="clr-namespace:INC.ViewModel.Constants;assembly=INC.ViewModel"
             xmlns:converters="clr-namespace:INC.View.Converters"
             d:DataContext="{d:DesignInstance mainPage:MainPageViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <converters:MenuTitleConverter x:Key="MenuTitleConverter" />
    </UserControl.Resources>
    <Grid>
        <!--  选项卡  -->
        <syncfusion:TabControlExt
                            regions:RegionManager.RegionName="{x:Static constants:RegionNames.Main}"
                            TabPanelBackground="#FFF3F3F3"
                            AllowDragDrop="False"
                            BorderThickness="0"
                            CloseButtonType="Individual"
                            EnableLabelEdit="False"
                            Margin="0"
                            OnCloseAllTabs="TabControlExt_OnCloseAllTabs"
                            OnCloseButtonClick="TabControlExt_OnCloseButtonClick"
                            OnCloseOtherTabs="TabControlExt_OnCloseOtherTabs"
                            SelectedIndex="{Binding NavigationManager.SelectedIndex}"
                            ShowTabListContextMenu="False">
            <syncfusion:TabControlExt.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=syncfusion:TabItemExt}, Converter={StaticResource MenuTitleConverter}}" />
                </DataTemplate>
            </syncfusion:TabControlExt.ItemTemplate>
        </syncfusion:TabControlExt>


        
        <!--  用户功能面板  -->

        <Border 
                Width="300"
                Margin="0,0,-305,0"
                HorizontalAlignment="Right"
                VerticalAlignment="Stretch"
                Background="WhiteSmoke"
                BorderThickness="0">
            <Border.Resources>
                <Storyboard x:Key="SlideIn">
                    <DoubleAnimation
                        Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                        From="0"
                        To=" -305"
                        Duration="0:0:0.3" />
                </Storyboard>
                <Storyboard x:Key="SlideOut">
                    <DoubleAnimation
                        Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                        From="-305"
                        To="0"
                        Duration="0:0:0.3" />
                </Storyboard>
            </Border.Resources>

            <Grid>
                <!--  用户功能面板  -->
                <ContentControl regions:RegionManager.RegionName="{x:Static constants:RegionNames.UserPanelView}" />
            </Grid>

            <Border.Style>
                <Style TargetType="{x:Type Border}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding HeaderFunctionViewModel.IsShowUserPanel}" Value="true">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource SlideIn}" />
                            </DataTrigger.EnterActions>
                            <DataTrigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource SlideOut}" />
                            </DataTrigger.ExitActions>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            <Border.RenderTransform>
                <TranslateTransform />
            </Border.RenderTransform>
        </Border>

    </Grid>
</UserControl>
