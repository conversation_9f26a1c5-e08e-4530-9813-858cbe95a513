﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\Images\menus\accounts.png" />
    <None Remove="Resources\Images\menus\download.png" />
    <None Remove="Resources\Images\menus\logout.png" />
    <None Remove="Resources\Images\menus\mysettings.png" />
    <None Remove="Resources\Images\menus\password.png" />
    <None Remove="Resources\Images\menus\role.png" />
    <None Remove="Resources\Images\menus\setting.png" />
    <None Remove="Resources\Images\menus\tenant.png" />
    <None Remove="Resources\Images\menus\user.png" />
    <None Remove="Resources\Images\menus\包装_白.png" />
    <None Remove="Resources\Images\menus\包装_黑.png" />
    <None Remove="Resources\Images\menus\已下发_白.png" />
    <None Remove="Resources\Images\menus\已下发_黑.png" />
    <None Remove="Resources\Images\menus\报废_包装白.png" />
    <None Remove="Resources\Images\menus\报废_包装黑.png" />
    <None Remove="Resources\Images\menus\报废_白.png" />
    <None Remove="Resources\Images\menus\报废_黑.png" />
    <None Remove="Resources\Images\menus\未下发_白.png" />
    <None Remove="Resources\Images\menus\未下发_黑.png" />
    <None Remove="Resources\Images\menus\缝纫_白.png" />
    <None Remove="Resources\Images\menus\缝纫_黑.png" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="logo.ico" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
	</ItemGroup>


	<ItemGroup>
    <ProjectReference Include="..\..\03_Desktop\INC.ViewModel\INC.ViewModel.csproj" />
    <ProjectReference Include="..\..\03_Desktop\INC.View\INC.View.csproj" />
    <ProjectReference Include="..\..\04_BusinessModules\INC.BusinessModuleCore\INC.BusinessModuleCore.csproj" />
    <ProjectReference Include="..\..\04_BusinessModules\INC.LoginBusinessModule\INC.LoginBusinessModule.csproj" />
    <ProjectReference Include="..\..\04_BusinessModules\INC.ParticleMixing\INC.HalfScrap.csproj" />
    <ProjectReference Include="..\..\04_BusinessModules\INC.ProductionExecution\INC.Production.csproj" />
    <ProjectReference Include="..\..\05_FunctionModules\INC.AutoUpdateFunctionModule\INC.AutoUpdateFunctionModule.csproj" />
    <ProjectReference Include="..\..\05_FunctionModules\INC.CompactFunctionModule\INC.CompactFunctionModule.csproj" />
    <ProjectReference Include="..\..\05_FunctionModules\INC.DeviceFunctionModule\INC.DeviceFunctionModule.csproj" />
    <ProjectReference Include="..\..\05_FunctionModules\INC.FunctionModuleCore\INC.FunctionModuleCore.csproj" />
    <ProjectReference Include="..\..\06_Infrustructures\INC.Common\INC.Common.csproj" />
    <ProjectReference Include="..\..\06_Infrustructures\INC.ViewCore\INC.ViewCore.csproj" />
    <ProjectReference Include="..\..\06_Infrustructures\INC.ViewModelCore\INC.ViewModelCore.csproj" />
  </ItemGroup>


	<ItemGroup>
	  <Resource Include="Resources\Images\menus\accounts.png" />
	  <Resource Include="Resources\Images\menus\download.png" />
	  <Resource Include="Resources\Images\menus\logout.png" />
	  <Resource Include="Resources\Images\menus\mysettings.png" />
	  <Resource Include="Resources\Images\menus\password.png" />
	  <Resource Include="Resources\Images\menus\role.png" />
	  <Resource Include="Resources\Images\menus\setting.png" />
	  <Resource Include="Resources\Images\menus\tenant.png" />
	  <Resource Include="Resources\Images\menus\user.png" />
	  <Resource Include="Resources\Images\menus\包装_白.png" />
	  <Resource Include="Resources\Images\menus\包装_黑.png" />
	  <Resource Include="Resources\Images\menus\已下发_白.png" />
	  <Resource Include="Resources\Images\menus\已下发_黑.png" />
	  <Resource Include="Resources\Images\menus\报废_包装白.png" />
	  <Resource Include="Resources\Images\menus\报废_包装黑.png" />
	  <Resource Include="Resources\Images\menus\报废_白.png" />
	  <Resource Include="Resources\Images\menus\报废_黑.png" />
	  <Resource Include="Resources\Images\menus\未下发_白.png" />
	  <Resource Include="Resources\Images\menus\未下发_黑.png" />
	  <Resource Include="Resources\Images\menus\缝纫_白.png" />
	  <Resource Include="Resources\Images\menus\缝纫_黑.png" />
	</ItemGroup>


	<ItemGroup>
	  <None Update="appsettings.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Reports\入库单.repx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Reports\工单标签.repx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Reports\箱麦二维码.repx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Reports\英文入库单.repx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
