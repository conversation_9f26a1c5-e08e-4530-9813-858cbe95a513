{"version": 3, "targets": {"net8.0-windows7.0": {"CommunityToolkit.Mvvm/8.3.2": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"type": "package", "compile": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Prism.Container.Abstractions/9.0.106": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "compile": {"lib/net8.0/Prism.Container.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Prism.Container.Abstractions.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Prism.Container.Abstractions.targets": {}}}, "Prism.Container.Unity/9.0.106": {"type": "package", "dependencies": {"Prism.Container.Abstractions": "9.0.106", "Unity.Container": "5.11.11"}, "compile": {"lib/net8.0/Prism.Container.Unity.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/Prism.Container.Unity.dll": {"related": ".pdb"}}, "build": {"buildTransitive/Prism.Container.Unity.targets": {}}}, "Prism.Core/9.0.537": {"type": "package", "dependencies": {"Prism.Container.Abstractions": "9.0.106", "Prism.Events": "9.0.537"}, "compile": {"lib/net6.0/Prism.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Prism.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Prism.Core.targets": {}}}, "Prism.Events/9.0.537": {"type": "package", "compile": {"lib/net6.0/Prism.Events.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Prism.Events.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Prism.Events.targets": {}}}, "Prism.Unity/9.0.537": {"type": "package", "dependencies": {"Prism.Container.Unity": "9.0.106", "Prism.Wpf": "9.0.537"}, "compile": {"lib/net6.0-windows7.0/Prism.Unity.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Prism.Unity.Wpf.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "build": {"buildTransitive/Prism.Unity.targets": {}}}, "Prism.Wpf/9.0.537": {"type": "package", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.122", "Prism.Core": "9.0.537"}, "compile": {"lib/net6.0-windows7.0/Prism.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Prism.Wpf.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Serilog/4.1.0": {"type": "package", "compile": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}}, "Syncfusion.Compression.Net.Core/27.2.4": {"type": "package", "compile": {"lib/net8.0/Syncfusion.Compression.Portable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.Compression.Portable.dll": {"related": ".xml"}}}, "Syncfusion.Data.WPF/27.2.4": {"type": "package", "compile": {"lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Licensing/28.2.3": {"type": "package", "compile": {"lib/net8.0/Syncfusion.Licensing.dll": {}}, "runtime": {"lib/net8.0/Syncfusion.Licensing.dll": {}}}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfGrid.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Data.WPF": "27.2.4", "Syncfusion.Licensing": "27.2.4", "Syncfusion.Shared.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll": {"related": ".xml"}}}, "Syncfusion.SfTreeView.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGridCommon.WPF": "27.2.4", "Syncfusion.Shared.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Shared.WPF/28.2.3": {"type": "package", "dependencies": {"Syncfusion.Licensing": "28.2.3"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Licensing": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll": {"related": ".xml"}}}, "Syncfusion.Tools.WPF/28.2.3": {"type": "package", "dependencies": {"Syncfusion.Licensing": "28.2.3", "Syncfusion.Shared.WPF": "28.2.3"}, "compile": {"lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll": {"related": ".xml"}}}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"type": "package", "dependencies": {"Syncfusion.Compression.Net.Core": "27.2.4", "Syncfusion.Licensing": "27.2.4"}, "compile": {"lib/net8.0/Syncfusion.XlsIO.Portable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Syncfusion.XlsIO.Portable.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "Unity.Abstractions/5.11.7": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netcoreapp3.0/Unity.Abstractions.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.0/Unity.Abstractions.dll": {"related": ".pdb"}}}, "Unity.Container/5.11.11": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2", "Unity.Abstractions": "5.11.7"}, "compile": {"lib/netcoreapp3.0/Unity.Container.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.0/Unity.Container.dll": {"related": ".pdb"}}}, "INC.BusinessModuleCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.FunctionModuleCore": "1.0.0", "INC.ViewCore": "1.0.0", "INC.ViewModelCore": "1.0.0"}, "compile": {"bin/placeholder/INC.BusinessModuleCore.dll": {}}, "runtime": {"bin/placeholder/INC.BusinessModuleCore.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Newtonsoft.Json": "13.0.1", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"bin/placeholder/INC.Common.dll": {}}, "runtime": {"bin/placeholder/INC.Common.dll": {}}}, "INC.FunctionModuleCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.Common": "1.0.0", "Prism.Core": "9.0.537", "Serilog": "4.1.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "Syncfusion.XlsIO.Net.Core": "27.2.4"}, "compile": {"bin/placeholder/INC.FunctionModuleCore.dll": {}}, "runtime": {"bin/placeholder/INC.FunctionModuleCore.dll": {}}}, "INC.LoginBusinessModule/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.BusinessModuleCore": "1.0.0"}, "compile": {"bin/placeholder/INC.LoginBusinessModule.dll": {}}, "runtime": {"bin/placeholder/INC.LoginBusinessModule.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.ViewCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"INC.ViewModelCore": "1.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.135", "Prism.Core": "9.0.537", "Prism.Unity": "9.0.537", "Syncfusion.Licensing": "27.2.4", "Syncfusion.SfBusyIndicator.WPF": "27.2.4", "Syncfusion.SfGrid.WPF": "27.2.4", "Syncfusion.SfSkinManager.WPF": "27.2.4", "Syncfusion.SfTreeView.WPF": "27.2.4", "Syncfusion.Themes.MaterialDark.WPF": "27.2.4", "Syncfusion.Themes.MaterialDarkBlue.WPF": "27.2.4", "Syncfusion.Themes.MaterialLight.WPF": "27.2.4", "Syncfusion.Themes.MaterialLightBlue.WPF": "27.2.4", "Syncfusion.Tools.WPF": "27.2.4"}, "compile": {"bin/placeholder/INC.ViewCore.dll": {}}, "runtime": {"bin/placeholder/INC.ViewCore.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "INC.ViewModelCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"CommunityToolkit.Mvvm": "8.3.2", "INC.Common": "1.0.0", "Prism.Core": "9.0.537"}, "compile": {"bin/placeholder/INC.ViewModelCore.dll": {}}, "runtime": {"bin/placeholder/INC.ViewModelCore.dll": {}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.3.2": {"sha512": "m8EolE1A0Updj68WTsZSGI6VWb6mUqHPh7QFo0kt7+JPhYMNXRS1ch8TS/oITAdcxTLrwMOp3ku1KjeG1/Zdpg==", "type": "package", "path": "communitytoolkit.mvvm/8.3.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.3.2.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net6.0/CommunityToolkit.Mvvm.dll", "lib/net6.0/CommunityToolkit.Mvvm.pdb", "lib/net6.0/CommunityToolkit.Mvvm.xml", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"sha512": "fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.135": {"sha512": "r8qBEXmQfORso2+MVHnt8PSH4761zJ0SIxgQTSEDVLU97EN2FZdG6/ZCYUPhQy+OrPKgnpYBCAs3PS6Bs7wRsg==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.135", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net462/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net462/Microsoft.Xaml.Behaviors.dll", "lib/net462/Microsoft.Xaml.Behaviors.pdb", "lib/net462/Microsoft.Xaml.Behaviors.xml", "lib/net6.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.135.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Prism.Container.Abstractions/9.0.106": {"sha512": "QNOERNOqsxvAa8pbWjqFB872DkvYK/cVRrcFO5vJYgWTIKBd8xfaI/jaZ0qeXLYVDz0nrvgJTZVVnip6+68dCw==", "type": "package", "path": "prism.container.abstractions/9.0.106", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Container.Abstractions.targets", "lib/net462/Prism.Container.Abstractions.dll", "lib/net462/Prism.Container.Abstractions.pdb", "lib/net47/Prism.Container.Abstractions.dll", "lib/net47/Prism.Container.Abstractions.pdb", "lib/net6.0/Prism.Container.Abstractions.dll", "lib/net6.0/Prism.Container.Abstractions.pdb", "lib/net7.0/Prism.Container.Abstractions.dll", "lib/net7.0/Prism.Container.Abstractions.pdb", "lib/net8.0/Prism.Container.Abstractions.dll", "lib/net8.0/Prism.Container.Abstractions.pdb", "lib/netstandard2.0/Prism.Container.Abstractions.dll", "lib/netstandard2.0/Prism.Container.Abstractions.pdb", "lib/netstandard2.1/Prism.Container.Abstractions.dll", "lib/netstandard2.1/Prism.Container.Abstractions.pdb", "prism-logo.png", "prism.container.abstractions.9.0.106.nupkg.sha512", "prism.container.abstractions.nuspec"]}, "Prism.Container.Unity/9.0.106": {"sha512": "QRakEz+1HG7PGETsEWQnHED4tmp7Ir/lVIVo0TySER1ACqNGNQgAfSgza+B/WMl/SadHhrz+HlTVQw3+PrAFWQ==", "type": "package", "path": "prism.container.unity/9.0.106", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Container.Unity.targets", "lib/net462/Prism.Container.Unity.dll", "lib/net462/Prism.Container.Unity.pdb", "lib/net6.0/Prism.Container.Unity.dll", "lib/net6.0/Prism.Container.Unity.pdb", "lib/net7.0/Prism.Container.Unity.dll", "lib/net7.0/Prism.Container.Unity.pdb", "lib/net8.0/Prism.Container.Unity.dll", "lib/net8.0/Prism.Container.Unity.pdb", "lib/netstandard2.0/Prism.Container.Unity.dll", "lib/netstandard2.0/Prism.Container.Unity.pdb", "prism-logo.png", "prism.container.unity.9.0.106.nupkg.sha512", "prism.container.unity.nuspec"]}, "Prism.Core/9.0.537": {"sha512": "D7mEqPKLVNrD0g2WHCpC/MOKwn8h7X1liCWyjqjL7NCuxgwuhVLTG85E/ZPBkISrXdwvOQZ+bSY31bvP79FQlg==", "type": "package", "path": "prism.core/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Core.targets", "lib/net462/Prism.dll", "lib/net462/Prism.pdb", "lib/net462/Prism.xml", "lib/net47/Prism.dll", "lib/net47/Prism.pdb", "lib/net47/Prism.xml", "lib/net6.0/Prism.dll", "lib/net6.0/Prism.pdb", "lib/net6.0/Prism.xml", "lib/netstandard2.0/Prism.dll", "lib/netstandard2.0/Prism.pdb", "lib/netstandard2.0/Prism.xml", "prism-logo.png", "prism.core.9.0.537.nupkg.sha512", "prism.core.nuspec"]}, "Prism.Events/9.0.537": {"sha512": "Pzp5MGUuhAyKXZUbHVYNWLGF/eA3sScqDN6VrzbWlKj85R0IS0q+JXe99umynso2xhXAe+1jrQCCkgqmEFCBng==", "type": "package", "path": "prism.events/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Events.targets", "lib/net462/Prism.Events.dll", "lib/net462/Prism.Events.pdb", "lib/net462/Prism.Events.xml", "lib/net47/Prism.Events.dll", "lib/net47/Prism.Events.pdb", "lib/net47/Prism.Events.xml", "lib/net6.0/Prism.Events.dll", "lib/net6.0/Prism.Events.pdb", "lib/net6.0/Prism.Events.xml", "lib/netstandard2.0/Prism.Events.dll", "lib/netstandard2.0/Prism.Events.pdb", "lib/netstandard2.0/Prism.Events.xml", "prism-logo.png", "prism.events.9.0.537.nupkg.sha512", "prism.events.nuspec"]}, "Prism.Unity/9.0.537": {"sha512": "F2RjW2QZg2TsQxuYsRB0ldoacQw2xuZmaMM1LENfR+qbxPxBXC887yZ+PKeP9eWPP2sP3oVUqo09N8EWJLZXng==", "type": "package", "path": "prism.unity/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "buildTransitive/Prism.Unity.targets", "lib/net462/Prism.Unity.Wpf.dll", "lib/net462/Prism.Unity.Wpf.pdb", "lib/net462/Prism.Unity.Wpf.xml", "lib/net47/Prism.Unity.Wpf.dll", "lib/net47/Prism.Unity.Wpf.pdb", "lib/net47/Prism.Unity.Wpf.xml", "lib/net6.0-windows7.0/Prism.Unity.Wpf.dll", "lib/net6.0-windows7.0/Prism.Unity.Wpf.pdb", "lib/net6.0-windows7.0/Prism.Unity.Wpf.xml", "prism-logo.png", "prism.unity.9.0.537.nupkg.sha512", "prism.unity.nuspec"]}, "Prism.Wpf/9.0.537": {"sha512": "srsXhi7FRUFawsNoRkY67duMEGjZo3ff0FpqpkjeWkkAuLazlH1UmNVrvwnpaLQCBboexH/z6oGrLvpeocxgdw==", "type": "package", "path": "prism.wpf/9.0.537", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "ReadMe.md", "lib/net462/Prism.Wpf.dll", "lib/net462/Prism.Wpf.pdb", "lib/net462/Prism.Wpf.xml", "lib/net47/Prism.Wpf.dll", "lib/net47/Prism.Wpf.pdb", "lib/net47/Prism.Wpf.xml", "lib/net6.0-windows7.0/Prism.Wpf.dll", "lib/net6.0-windows7.0/Prism.Wpf.pdb", "lib/net6.0-windows7.0/Prism.Wpf.xml", "prism-logo.png", "prism.wpf.9.0.537.nupkg.sha512", "prism.wpf.nuspec"]}, "Serilog/4.1.0": {"sha512": "u1aZI8HZ62LWlq5dZLFwm6jMax/sUwnWZSw5lkPsCt518cJBxFKoNmc7oSxe5aA5BgSkzy9rzwFGR/i/acnSPw==", "type": "package", "path": "serilog/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.1.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.Sinks.Console/6.0.0": {"sha512": "fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "type": "package", "path": "serilog.sinks.console/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net8.0/Serilog.Sinks.Console.dll", "lib/net8.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.6.0.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.File/6.0.0": {"sha512": "lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "type": "package", "path": "serilog.sinks.file/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.File.dll", "lib/net462/Serilog.Sinks.File.xml", "lib/net471/Serilog.Sinks.File.dll", "lib/net471/Serilog.Sinks.File.xml", "lib/net6.0/Serilog.Sinks.File.dll", "lib/net6.0/Serilog.Sinks.File.xml", "lib/net8.0/Serilog.Sinks.File.dll", "lib/net8.0/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.xml", "serilog-sink-nuget.png", "serilog.sinks.file.6.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Syncfusion.Compression.Net.Core/27.2.4": {"sha512": "1+bVigMEnIxAbqVoIpIwOmX/LzJpHFCcSLJLsPo1x3Jk8khAXXPbZg/reH8mIPDEYVVNIifmx3RE6VJZz3/Uzw==", "type": "package", "path": "syncfusion.compression.net.core/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net6.0/Syncfusion.Compression.Portable.dll", "lib/net6.0/Syncfusion.Compression.Portable.xml", "lib/net7.0/Syncfusion.Compression.Portable.dll", "lib/net7.0/Syncfusion.Compression.Portable.xml", "lib/net8.0/Syncfusion.Compression.Portable.dll", "lib/net8.0/Syncfusion.Compression.Portable.xml", "lib/net9.0/Syncfusion.Compression.Portable.dll", "lib/net9.0/Syncfusion.Compression.Portable.xml", "lib/netstandard2.0/Syncfusion.Compression.Portable.dll", "lib/netstandard2.0/Syncfusion.Compression.Portable.xml", "syncfusion.compression.net.core.27.2.4.nupkg.sha512", "syncfusion.compression.net.core.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Data.WPF/27.2.4": {"sha512": "7KPOZZXA8aNGOX1V0VY6hV3X8xX2Fyu+Dcbe7VEIcZhSTS2E/CZ7PNNmczParX+VmDyz1tD7k8mFHEN5NPf4kg==", "type": "package", "path": "syncfusion.data.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net40/Syncfusion.Data.WPF.dll", "lib/net40/Syncfusion.Data.WPF.xml", "lib/net462/Syncfusion.Data.WPF.dll", "lib/net462/Syncfusion.Data.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Data.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Data.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Data.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Data.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Data.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Data.WPF.xml", "syncfusion.data.wpf.27.2.4.nupkg.sha512", "syncfusion.data.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Licensing/28.2.3": {"sha512": "EjsEHdkZwvViixabnrb2ZGmvg/a79Y01gLuOQzoMrRNUaOkQ/c71B+7eHoLUEJEt7H7OWNhlcKcbGtTmA75MBA==", "type": "package", "path": "syncfusion.licensing/28.2.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/MonoAndroid90/Syncfusion.Licensing.dll", "lib/Xamarin.Mac/Syncfusion.Licensing.dll", "lib/Xamarin.iOS10/Syncfusion.Licensing.dll", "lib/net462/Syncfusion.Licensing.dll", "lib/net6.0/Syncfusion.Licensing.dll", "lib/net7.0/Syncfusion.Licensing.dll", "lib/net8.0/Syncfusion.Licensing.dll", "lib/net9.0/Syncfusion.Licensing.dll", "lib/netstandard2.0/Syncfusion.Licensing.dll", "lib/uap10.0/Syncfusion.Licensing.dll", "syncfusion.licensing.28.2.3.nupkg.sha512", "syncfusion.licensing.nuspec", "syncfusion_logo.png"]}, "Syncfusion.SfBusyIndicator.WPF/27.2.4": {"sha512": "z06nYGZkmeChFD1/ct8dD7N642LXUM26H2lJoRq/p5kJlkETi3ax1p0DzkdksXMNvSN/V1yr+Vf2NLPEGTX9IQ==", "type": "package", "path": "syncfusion.sfbusyindicator.wpf/27.2.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Design/Syncfusion.Design.Wpf.dll", "lib/net40/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net40/Design/Syncfusion.SfBusyIndicator.WPF.Expression.Design.dll", "lib/net40/Design/Syncfusion.SfBusyIndicator.Wpf.VisualStudio.Design.dll", "lib/net40/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net40/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net462/Design/Syncfusion.Design.Wpf.dll", "lib/net462/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.SfBusyIndicator.WPF.Expression.Design.dll", "lib/net462/Design/Syncfusion.SfBusyIndicator.Wpf.VisualStudio.Design.dll", "lib/net462/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net462/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.SfBusyIndicator.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfBusyIndicator.WPF.xml", "syncfusion.sfbusyindicator.wpf.27.2.4.nupkg.sha512", "syncfusion.sfbusyindicator.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.SfGrid.WPF/27.2.4": {"sha512": "852wa/nHP9/lCxg0p1Dyy+oqepcFwXNqpJzrBEyKTbKpHAN2ly8z2QjViNXfvOw7zjXh4OftGL2RUlWTrK6CSQ==", "type": "package", "path": "syncfusion.sfgrid.wpf/27.2.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net40/Design/Syncfusion.SfGrid.WPF.Expression.Design.dll", "lib/net40/Design/Syncfusion.SfGrid.WPF.VisualStudio.Design.dll", "lib/net40/Syncfusion.SfGrid.WPF.dll", "lib/net40/Syncfusion.SfGrid.WPF.xml", "lib/net462/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.SfGrid.WPF.Expression.Design.dll", "lib/net462/Design/Syncfusion.SfGrid.WPF.VisualStudio.Design.dll", "lib/net462/Syncfusion.SfGrid.WPF.dll", "lib/net462/Syncfusion.SfGrid.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.SfGrid.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfGrid.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfGrid.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.SfGrid.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.SfGrid.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfGrid.WPF.xml", "syncfusion.sfgrid.wpf.27.2.4.nupkg.sha512", "syncfusion.sfgrid.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.SfGridCommon.WPF/27.2.4": {"sha512": "I/IXKLY3rmzgbIsETpf7bz2HEVnEjDS7MUaQ9IgzCENzwrA8K42FMAFO3KZeWJmTGQZnR3YG+ukPfZsz18LGWA==", "type": "package", "path": "syncfusion.sfgridcommon.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net40/Syncfusion.SfGridCommon.WPF.dll", "lib/net40/Syncfusion.SfGridCommon.WPF.xml", "lib/net462/Syncfusion.SfGridCommon.WPF.dll", "lib/net462/Syncfusion.SfGridCommon.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfGridCommon.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfGridCommon.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.SfGridCommon.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfGridCommon.WPF.xml", "syncfusion.sfgridcommon.wpf.27.2.4.nupkg.sha512", "syncfusion.sfgridcommon.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.SfSkinManager.WPF/27.2.4": {"sha512": "omu/q1JaEydjM8F+WWIY7SGCa8/KMa87RfGaTIVMnWHru0b/Zcv6u/VsiSPuVTijMpp1shu+Kh5Phm0GnDRufA==", "type": "package", "path": "syncfusion.sfskinmanager.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.SfSkinManager.WPF.dll", "lib/net40/Syncfusion.SfSkinManager.WPF.xml", "lib/net462/Syncfusion.SfSkinManager.WPF.dll", "lib/net462/Syncfusion.SfSkinManager.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.SfSkinManager.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfSkinManager.WPF.xml", "syncfusion.sfskinmanager.wpf.27.2.4.nupkg.sha512", "syncfusion.sfskinmanager.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.SfTreeView.WPF/27.2.4": {"sha512": "LC/DZc7UG/5eBlL3HDjMGO5JzDRtjguH+yVttooY5odg6e9hP5X0znsSF83EcnOkwH7s0JR6OLnchBUQz9eu7w==", "type": "package", "path": "syncfusion.sftreeview.wpf/27.2.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net40/Design/Syncfusion.SfTreeView.WPF.Expression.Design.dll", "lib/net40/Design/Syncfusion.SfTreeView.WPF.VisualStudio.Design.dll", "lib/net40/Design/Syncfusion.SfTreeView.WPF.dll.Design.dll", "lib/net40/Syncfusion.SfTreeView.WPF.dll", "lib/net40/Syncfusion.SfTreeView.WPF.xml", "lib/net462/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.SfTreeView.WPF.Expression.Design.dll", "lib/net462/Design/Syncfusion.SfTreeView.WPF.VisualStudio.Design.dll", "lib/net462/Design/Syncfusion.SfTreeView.WPF.dll.Design.dll", "lib/net462/Syncfusion.SfTreeView.WPF.dll", "lib/net462/Syncfusion.SfTreeView.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.SfTreeView.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.SfTreeView.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.SfTreeView.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.SfTreeView.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.SfTreeView.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.SfTreeView.WPF.xml", "syncfusion.sftreeview.wpf.27.2.4.nupkg.sha512", "syncfusion.sftreeview.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.Shared.WPF/28.2.3": {"sha512": "qHQ2RH6d7vUf3NCAzHpOykzIYcZO6TfSA4DIrBis9TN3sl0g7u4WV3YMtt9szcRAGWBXqosCozwMbIbBh0IpgQ==", "type": "package", "path": "syncfusion.shared.wpf/28.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net462/Design/Syncfusion.Design.Wpf.dll", "lib/net462/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.Expression.Design.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.VisualStudio.Design.dll", "lib/net462/Design/Syncfusion.Shared.Wpf.dll.Design.dll", "lib/net462/Syncfusion.Shared.WPF.dll", "lib/net462/Syncfusion.Shared.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Shared.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Shared.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.Shared.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.Shared.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Shared.WPF.xml", "syncfusion.shared.wpf.28.2.3.nupkg.sha512", "syncfusion.shared.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.Themes.MaterialDark.WPF/27.2.4": {"sha512": "HFtbwZB+ZwzWP2K6UhJZDA4ORHHYs5evUwPwAC/UnL//05mWnYYKQY8+IXn2Hx4Wg1pIRUn/fc+sYvGvSS3AvQ==", "type": "package", "path": "syncfusion.themes.materialdark.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDark.WPF.xml", "syncfusion.themes.materialdark.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materialdark.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Themes.MaterialDarkBlue.WPF/27.2.4": {"sha512": "9ickWilfXrisH+bk1w4fZxbASCZQoANjbKulQ1KG8G1WdWj3M+wbrBIQ4dK577kbNpd9NtXJJK5NXuIJFWBkEQ==", "type": "package", "path": "syncfusion.themes.materialdarkblue.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialDarkBlue.WPF.xml", "syncfusion.themes.materialdarkblue.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materialdarkblue.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Themes.MaterialLight.WPF/27.2.4": {"sha512": "OO6N/u+pzNVkY50uGSCz14ZSTA2PY38Qw4L9dehLm3pVOe1+4PBDQDxlaV2TWyfw+yaUF+sX2WTyxPfl23QGGw==", "type": "package", "path": "syncfusion.themes.materiallight.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLight.WPF.xml", "syncfusion.themes.materiallight.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materiallight.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Themes.MaterialLightBlue.WPF/27.2.4": {"sha512": "wOdsaGE/gUtrJHiSJ4j0D2UkXAb0gvJQ0+91SOypmH5N8Z1ytIwW6XujZKBxAp95lm+PZV0sXLekmJ4nGaU6nQ==", "type": "package", "path": "syncfusion.themes.materiallightblue.wpf/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net40/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net40/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net462/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net462/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Themes.MaterialLightBlue.WPF.xml", "syncfusion.themes.materiallightblue.wpf.27.2.4.nupkg.sha512", "syncfusion.themes.materiallightblue.wpf.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Tools.WPF/28.2.3": {"sha512": "vfAblki/RZQa8TBESUb0yF8iKwmURXJ4nl5rLRnp2W7FkHirLEitK6hTN3Jr7C9qjaDsECzFPK/3g4thHJhXiw==", "type": "package", "path": "syncfusion.tools.wpf/28.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net462/Design/Syncfusion.Design.Wpf.dll", "lib/net462/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net462/Design/Syncfusion.Tools.Wpf.Expression.Design.dll", "lib/net462/Design/Syncfusion.Tools.Wpf.VisualStudio.Design.dll", "lib/net462/Design/Syncfusion.Tools.Wpf.dll.Design.dll", "lib/net462/Syncfusion.Tools.WPF.dll", "lib/net462/Syncfusion.Tools.WPF.xml", "lib/net6.0-windows7.0/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net6.0-windows7.0/Syncfusion.Tools.WPF.dll", "lib/net6.0-windows7.0/Syncfusion.Tools.WPF.xml", "lib/net8.0-windows7.0/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net8.0-windows7.0/Syncfusion.Tools.WPF.dll", "lib/net8.0-windows7.0/Syncfusion.Tools.WPF.xml", "lib/net9.0-windows7.0/Design/Syncfusion.Tools.WPF.DesignTools.dll", "lib/net9.0-windows7.0/Syncfusion.Tools.WPF.dll", "lib/net9.0-windows7.0/Syncfusion.Tools.WPF.xml", "syncfusion.tools.wpf.28.2.3.nupkg.sha512", "syncfusion.tools.wpf.nuspec", "syncfusion_logo.png", "tools/VisualStudioToolsManifest.xml"]}, "Syncfusion.XlsIO.Net.Core/27.2.4": {"sha512": "IlY9QaOZhjl+rJ0Q+I8PZZ7s5d26/Uw3hzQ9t08hEXfuq/kEkty3zbTmkYQZTOq6L4kaXjwApAGnQwxDCsPsow==", "type": "package", "path": "syncfusion.xlsio.net.core/27.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/net6.0/Syncfusion.XlsIO.Portable.dll", "lib/net6.0/Syncfusion.XlsIO.Portable.xml", "lib/net7.0/Syncfusion.XlsIO.Portable.dll", "lib/net7.0/Syncfusion.XlsIO.Portable.xml", "lib/net8.0/Syncfusion.XlsIO.Portable.dll", "lib/net8.0/Syncfusion.XlsIO.Portable.xml", "lib/net9.0/Syncfusion.XlsIO.Portable.dll", "lib/net9.0/Syncfusion.XlsIO.Portable.xml", "lib/netstandard2.0/Syncfusion.XlsIO.Portable.dll", "lib/netstandard2.0/Syncfusion.XlsIO.Portable.xml", "syncfusion.xlsio.net.core.27.2.4.nupkg.sha512", "syncfusion.xlsio.net.core.nuspec", "syncfusion_logo.png"]}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"sha512": "wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Extensions/4.5.2": {"sha512": "BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "type": "package", "path": "system.threading.tasks.extensions/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.2.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Unity.Abstractions/5.11.7": {"sha512": "3ztwGEpe35UJlCUswXoi4uVDp8bJsgPsOmO71nZnNXh51II7t54AbezDbS6sR2z4QnMOpNGDaXbsEkyg6dIfOQ==", "type": "package", "path": "unity.abstractions/5.11.7", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Unity.Abstractions.dll", "lib/net40/Unity.Abstractions.pdb", "lib/net45/Unity.Abstractions.dll", "lib/net45/Unity.Abstractions.pdb", "lib/net46/Unity.Abstractions.dll", "lib/net46/Unity.Abstractions.pdb", "lib/net47/Unity.Abstractions.dll", "lib/net47/Unity.Abstractions.pdb", "lib/net48/Unity.Abstractions.dll", "lib/net48/Unity.Abstractions.pdb", "lib/netcoreapp1.0/Unity.Abstractions.dll", "lib/netcoreapp1.0/Unity.Abstractions.pdb", "lib/netcoreapp2.0/Unity.Abstractions.dll", "lib/netcoreapp2.0/Unity.Abstractions.pdb", "lib/netcoreapp3.0/Unity.Abstractions.dll", "lib/netcoreapp3.0/Unity.Abstractions.pdb", "lib/netstandard1.0/Unity.Abstractions.dll", "lib/netstandard1.0/Unity.Abstractions.pdb", "lib/netstandard2.0/Unity.Abstractions.dll", "lib/netstandard2.0/Unity.Abstractions.pdb", "unity.abstractions.5.11.7.nupkg.sha512", "unity.abstractions.nuspec"]}, "Unity.Container/5.11.11": {"sha512": "47u4MBG8hxV2ZBUK7LlXcZQW8yWSqUSCRG+2/TBA2CSkxkQlMfVUJ0RJODJsZgsiSgy4N0M8HIr7J88drYR/OQ==", "type": "package", "path": "unity.container/5.11.11", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Unity.Container.dll", "lib/net40/Unity.Container.pdb", "lib/net45/Unity.Container.dll", "lib/net45/Unity.Container.pdb", "lib/net46/Unity.Container.dll", "lib/net46/Unity.Container.pdb", "lib/net47/Unity.Container.dll", "lib/net47/Unity.Container.pdb", "lib/net48/Unity.Container.dll", "lib/net48/Unity.Container.pdb", "lib/netcoreapp1.0/Unity.Container.dll", "lib/netcoreapp1.0/Unity.Container.pdb", "lib/netcoreapp2.0/Unity.Container.dll", "lib/netcoreapp2.0/Unity.Container.pdb", "lib/netcoreapp3.0/Unity.Container.dll", "lib/netcoreapp3.0/Unity.Container.pdb", "lib/netstandard1.0/Unity.Container.dll", "lib/netstandard1.0/Unity.Container.pdb", "lib/netstandard2.0/Unity.Container.dll", "lib/netstandard2.0/Unity.Container.pdb", "unity.container.5.11.11.nupkg.sha512", "unity.container.nuspec"]}, "INC.BusinessModuleCore/1.0.0": {"type": "project", "path": "../INC.BusinessModuleCore/INC.BusinessModuleCore.csproj", "msbuildProject": "../INC.BusinessModuleCore/INC.BusinessModuleCore.csproj"}, "INC.Common/1.0.0": {"type": "project", "path": "../../06_Infrustructures/INC.Common/INC.Common.csproj", "msbuildProject": "../../06_Infrustructures/INC.Common/INC.Common.csproj"}, "INC.FunctionModuleCore/1.0.0": {"type": "project", "path": "../../05_FunctionModules/INC.FunctionModuleCore/INC.FunctionModuleCore.csproj", "msbuildProject": "../../05_FunctionModules/INC.FunctionModuleCore/INC.FunctionModuleCore.csproj"}, "INC.LoginBusinessModule/1.0.0": {"type": "project", "path": "../INC.LoginBusinessModule/INC.LoginBusinessModule.csproj", "msbuildProject": "../INC.LoginBusinessModule/INC.LoginBusinessModule.csproj"}, "INC.ViewCore/1.0.0": {"type": "project", "path": "../../06_Infrustructures/INC.ViewCore/INC.ViewCore.csproj", "msbuildProject": "../../06_Infrustructures/INC.ViewCore/INC.ViewCore.csproj"}, "INC.ViewModelCore/1.0.0": {"type": "project", "path": "../../06_Infrustructures/INC.ViewModelCore/INC.ViewModelCore.csproj", "msbuildProject": "../../06_Infrustructures/INC.ViewModelCore/INC.ViewModelCore.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["INC.BusinessModuleCore >= 1.0.0", "INC.LoginBusinessModule >= 1.0.0", "Syncfusion.Tools.WPF >= 28.2.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj", "projectName": "INC.Production", "projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\INC.Production.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.ProductionExecution\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.BusinessModuleCore\\INC.BusinessModuleCore.csproj"}, "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj": {"projectPath": "C:\\Users\\<USER>\\Source\\Workspaces\\006_JH_MES\\Desktop\\04_BusinessModules\\INC.LoginBusinessModule\\INC.LoginBusinessModule.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Syncfusion.Tools.WPF": {"target": "Package", "version": "[28.2.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}